"use client";

import React, { useState, useEffect, useCallback, memo } from "react";
import Image from "next/image";
import { signOut } from "next-auth/react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Pie, Doughnut } from 'react-chartjs-2';
import { Plus } from 'lucide-react';
import KpiSemanalModal from './KpiSemanalModal';
import KpiComprasModal from './KpiComprasModal';
import KpiHistorySection from './KpiHistorySection';
import K<PERSON>ComprasHistorySection from './KpiComprasHistorySection';
import ConfirmDialog from './ui/ConfirmDialog';
import { getKpisSemanales, createKpiSemanal, updateKpiSemanal, deleteKpiSemanal, type KpiSemanalData } from '@/app/actions/kpis-semanales';
import { getKpis<PERSON>om<PERSON>s, createKpi<PERSON>ompras, update<PERSON><PERSON><PERSON><PERSON>pras, deleteKpiCompras, type KpiComprasData } from '@/app/actions/kpis-compras';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);
import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  Droplet,
  Menu,
  X,
  ShoppingCart,
  Package,
  Truck,
  Calculator,
  Settings,
  Scale,
  Target,
  Banknote,
  Info,
  Download,
  Eye,
  Filter,
  Trash2
} from "lucide-react";

import appLogo from '@/assets/images/logo-combustibles-cassiopeia.png';

interface AdminDashboardProps {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  };
}

interface DashboardData {
  precios: any[];
  terminales: any[];
  leads: any[];
  tarifas: any[];
}

const AdminDashboard = ({ user }: AdminDashboardProps) => {
  // Datos de ejemplo para el dashboard
  const mockData = {
    preciosRecientes: [
      { fecha: "2024-01", precio: 22.50, terminal: "Terminal A" },
      { fecha: "2024-02", precio: 23.10, terminal: "Terminal A" },
      { fecha: "2024-03", precio: 22.80, terminal: "Terminal A" },
      { fecha: "2024-04", precio: 24.20, terminal: "Terminal A" },
      { fecha: "2024-05", precio: 23.90, terminal: "Terminal A" },
      { fecha: "2024-06", precio: 25.10, terminal: "Terminal A" },
    ],
    ventasPorTerminal: [
      { terminal: "Terminal Norte", ventas: 1250, color: "#8884d8" },
      { terminal: "Terminal Sur", ventas: 980, color: "#82ca9d" },
      { terminal: "Terminal Este", ventas: 1100, color: "#ffc658" },
      { terminal: "Terminal Oeste", ventas: 850, color: "#ff7300" },
    ],
    leadsConversion: [
      { mes: "Ene", leads: 120, conversiones: 45 },
      { mes: "Feb", leads: 150, conversiones: 62 },
      { mes: "Mar", leads: 180, conversiones: 78 },
      { mes: "Abr", leads: 200, conversiones: 85 },
      { mes: "May", leads: 165, conversiones: 71 },
      { mes: "Jun", leads: 190, conversiones: 89 },
    ],
    estadisticasGenerales: {
      totalUsuarios: 1247,
      totalLeads: 3456,
      precioPromedio: 23.85,
      terminalesActivas: 12
    },
    indicadoresVentas: {
      volumenTotalLitros: 2450000,
      crecimientoMensual: 8.5,
      margenBrutoPorLitro: 2.35,
      tasaRetencionClientes: 87.2,
      cumplimientoObjetivo: 94.8,
      desviacionVentas: -5.2,
      cicloPromedioCierre: 12,
      clientesActivosMensuales: 1847
    },
    evolucionIndicadores: [
      { mes: "Ene", volumen: 2200000, crecimiento: 5.2, margen: 2.15, retencion: 85.5, cumplimiento: 92.3, desviacion: -3.1, ciclo: 14, clientesActivos: 1650 },
      { mes: "Feb", volumen: 2350000, crecimiento: 6.8, margen: 2.25, retencion: 86.2, cumplimiento: 93.1, desviacion: -2.8, ciclo: 13, clientesActivos: 1720 },
      { mes: "Mar", volumen: 2280000, crecimiento: -3.0, margen: 2.18, retencion: 84.8, cumplimiento: 91.7, desviacion: -4.2, ciclo: 15, clientesActivos: 1680 },
      { mes: "Abr", volumen: 2420000, crecimiento: 6.1, margen: 2.32, retencion: 87.1, cumplimiento: 94.2, desviacion: -1.9, ciclo: 11, clientesActivos: 1780 },
      { mes: "May", volumen: 2380000, crecimiento: -1.7, margen: 2.28, retencion: 86.8, cumplimiento: 93.8, desviacion: -3.5, ciclo: 13, clientesActivos: 1820 },
      { mes: "Jun", volumen: 2450000, crecimiento: 2.9, margen: 2.35, retencion: 87.2, cumplimiento: 94.8, desviacion: -2.1, ciclo: 12, clientesActivos: 1847 },
    ],
    distribucionCumplimiento: [
      { name: "Cumplido", value: 94.8, color: "#10b981" },
      { name: "Pendiente", value: 5.2, color: "#f59e0b" }
    ],
    distribucionRetencion: [
      { name: "Clientes Retenidos", value: 87.2, color: "#3b82f6" },
      { name: "Clientes Perdidos", value: 12.8, color: "#ef4444" }
    ],
    // Datos mock para el panel de Compras
    indicadoresCompras: {
      numeroProveedoresActivos: 24,
      porcentajeReporteGanancia: 92.5,
      preciosPromedioCompra: 21.85,
      diferencialPrecioPemex: -1.25,
      porcentajeCompraPorProveedor: [
        { proveedor: "Proveedor A", porcentaje: 35.2, color: "#3b82f6" },
        { proveedor: "Proveedor B", porcentaje: 28.7, color: "#10b981" },
        { proveedor: "Proveedor C", porcentaje: 18.5, color: "#f59e0b" },
        { proveedor: "Proveedor D", porcentaje: 12.3, color: "#8b5cf6" },
        { proveedor: "Otros", porcentaje: 5.3, color: "#ef4444" }
      ]
    },
    evolucionIndicadoresCompras: [
      { mes: "Ene", proveedores: 22, reporteGanancia: 89.2, precioPromedio: 21.45, diferencial: -1.15, porcentajeA: 32.1, porcentajeB: 30.2, porcentajeC: 20.1, porcentajeD: 12.6, porcentajeOtros: 5.0 },
      { mes: "Feb", proveedores: 23, reporteGanancia: 91.1, precioPromedio: 21.62, diferencial: -1.18, porcentajeA: 33.5, porcentajeB: 29.8, porcentajeC: 19.2, porcentajeD: 12.1, porcentajeOtros: 5.4 },
      { mes: "Mar", proveedores: 21, reporteGanancia: 88.7, precioPromedio: 21.38, diferencial: -1.32, porcentajeA: 31.8, porcentajeB: 31.1, porcentajeC: 19.8, porcentajeD: 12.8, porcentajeOtros: 4.5 },
      { mes: "Abr", proveedores: 24, reporteGanancia: 93.2, precioPromedio: 21.78, diferencial: -1.22, porcentajeA: 34.2, porcentajeB: 28.5, porcentajeC: 18.9, porcentajeD: 12.7, porcentajeOtros: 5.7 },
      { mes: "May", proveedores: 23, reporteGanancia: 90.8, precioPromedio: 21.71, diferencial: -1.28, porcentajeA: 33.8, porcentajeB: 29.1, porcentajeC: 18.2, porcentajeD: 13.1, porcentajeOtros: 5.8 },
      { mes: "Jun", proveedores: 24, reporteGanancia: 92.5, precioPromedio: 21.85, diferencial: -1.25, porcentajeA: 35.2, porcentajeB: 28.7, porcentajeC: 18.5, porcentajeD: 12.3, porcentajeOtros: 5.3 },
    ]
  };

  // Estados del componente
  const [activeTab, setActiveTab] = useState("ventas");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    precios: [],
    terminales: [],
    leads: [],
    tarifas: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("4w");
  const [isKpiModalOpen, setIsKpiModalOpen] = useState(false);
  const [kpisSemanales, setKpisSemanales] = useState<any[]>([]);
  const [loadingKpis, setLoadingKpis] = useState(false);
  const [hasRealData, setHasRealData] = useState(false);
  const [indicadoresActuales, setIndicadoresActuales] = useState(mockData.indicadoresVentas);
  const [evolucionActual, setEvolucionActual] = useState(mockData.evolucionIndicadores);
  const [distribucionCumplimientoActual, setDistribucionCumplimientoActual] = useState(mockData.distribucionCumplimiento);
  const [distribucionRetencionActual, setDistribucionRetencionActual] = useState(mockData.distribucionRetencion);

  // Estados específicos para el panel de Compras
  const [timeRangeCompras, setTimeRangeCompras] = useState("4w");
  const [isKpiModalOpenCompras, setIsKpiModalOpenCompras] = useState(false);
  const [showKpiHistoryCompras, setShowKpiHistoryCompras] = useState(false);
  const [indicadoresComprasActuales, setIndicadoresComprasActuales] = useState(mockData.indicadoresCompras);
  const [evolucionComprasActual, setEvolucionComprasActual] = useState<any[]>(mockData.evolucionIndicadoresCompras);
  const [kpisCompras, setKpisCompras] = useState<any[]>([]);
  const [loadingKpisCompras, setLoadingKpisCompras] = useState(false);
  const [editingKpiCompras, setEditingKpiCompras] = useState<KpiComprasData | null>(null);
  const [isAddingOldWeekCompras, setIsAddingOldWeekCompras] = useState(false);
  const [showAnalyticsCompras, setShowAnalyticsCompras] = useState(false);
  const [showFiltersCompras, setShowFiltersCompras] = useState(false);
  const [selectedKpisCompras, setSelectedKpisCompras] = useState<string[]>([]);
  const [editingKpi, setEditingKpi] = useState<KpiSemanalData | null>(null);

  // Estados para el diálogo de gráficas
  const [chartDialog, setChartDialog] = useState({
    isOpen: false,
    title: '',
    chartData: null as any,
    chartOptions: null as any,
    chartType: 'line' as 'line' | 'pie' | 'doughnut'
  });
  const [showKpiHistory, setShowKpiHistory] = useState(false); // Mostrar por defecto
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isAddingOldWeek, setIsAddingOldWeek] = useState(false);
  const [selectedKpis, setSelectedKpis] = useState<string[]>([]);

  // Estados para el dialog de confirmación
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    loading: false
  });

  // Función para abrir el diálogo de gráficas
  const openChartDialog = (title: string, chartData: any, chartOptions: any, chartType: 'line' | 'pie' | 'doughnut') => {
    setChartDialog({
      isOpen: true,
      title,
      chartData,
      chartOptions: {
        ...chartOptions,
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          ...chartOptions.plugins,
          legend: {
            display: true,
            position: 'bottom' as const,
            labels: {
              padding: 20,
              usePointStyle: true,
              font: {
                size: 12
              }
            }
          }
        }
      },
      chartType
    });
  };

  // Funciones para manejar KPIs semanales
  const loadKpisSemanales = async () => {
    setLoadingKpis(true);
    try {
      const result = await getKpisSemanales({ limit: 10 });
      if (result.success && result.data) {
        const kpis = result.data;
        setKpisSemanales(kpis);

        // No mostrar automáticamente el historial al cargar

        // Actualizar estados con datos reales si existen
        if (kpis.length > 0) {
          const latestKpi = kpis[0];

          // Actualizar indicadores principales con datos reales
          const nuevosIndicadores = {
            volumenTotalLitros: latestKpi.volumenTotalLitros,
            crecimientoMensual: latestKpi.crecimientoMensual,
            margenBrutoPorLitro: latestKpi.margenBrutoPorLitro,
            tasaRetencionClientes: latestKpi.tasaRetencionClientes,
            cumplimientoObjetivo: latestKpi.cumplimientoObjetivo,
            desviacionVentas: latestKpi.desviacionVentas,
            cicloPromedioCierre: latestKpi.cicloPromedioCierre,
            clientesActivosMensuales: latestKpi.clientesActivosMensuales
          };
          setIndicadoresActuales(nuevosIndicadores);

          // Actualizar evolución de indicadores con datos reales (últimas 6 semanas)
          const evolutionData = kpis.slice(0, 6).reverse().map((kpi: any) => ({
            mes: `S${kpi.weekNumber}`,
            volumen: kpi.volumenTotalLitros,
            crecimiento: kpi.crecimientoMensual,
            margen: kpi.margenBrutoPorLitro,
            retencion: kpi.tasaRetencionClientes,
            cumplimiento: kpi.cumplimientoObjetivo,
            desviacion: kpi.desviacionVentas,
            ciclo: kpi.cicloPromedioCierre,
            clientesActivos: kpi.clientesActivosMensuales
          }));

          // Si tenemos menos de 6 semanas de datos, completar con datos mock
          while (evolutionData.length < 6) {
            const lastIndex = evolutionData.length;
            evolutionData.unshift({
              mes: `S${27 - lastIndex}`,
              volumen: 2200000 + (lastIndex * 50000),
              crecimiento: 5.2 + (lastIndex * 0.5),
              margen: 2.15 + (lastIndex * 0.05),
              retencion: 85.5 + (lastIndex * 0.3),
              cumplimiento: 92.3 + (lastIndex * 0.4),
              desviacion: -3.1 - (lastIndex * 0.2),
              ciclo: 14 - lastIndex,
              clientesActivos: 1650 + (lastIndex * 30)
            });
          }

          setEvolucionActual(evolutionData);

          // Actualizar distribuciones con datos reales
          const nuevaDistribucionCumplimiento = [
            { name: "Cumplido", value: latestKpi.cumplimientoObjetivo, color: "#10b981" },
            { name: "Pendiente", value: Math.max(0, 100 - latestKpi.cumplimientoObjetivo), color: "#f59e0b" }
          ];
          setDistribucionCumplimientoActual(nuevaDistribucionCumplimiento);

          const nuevaDistribucionRetencion = [
            { name: "Clientes Retenidos", value: latestKpi.tasaRetencionClientes, color: "#3b82f6" },
            { name: "Clientes Perdidos", value: Math.max(0, 100 - latestKpi.tasaRetencionClientes), color: "#ef4444" }
          ];
          setDistribucionRetencionActual(nuevaDistribucionRetencion);

          setHasRealData(true);
        } else {
          setHasRealData(false);
        }
      }
    } catch (error) {
      console.error('Error al cargar KPIs semanales:', error);
    } finally {
      setLoadingKpis(false);
    }
  };

  const handleSaveKpi = async (kpiData: KpiSemanalData) => {
    try {
      let result;

      if (editingKpi && editingKpi.id) {
        // Actualizar KPI existente
        result = await updateKpiSemanal(editingKpi.id, kpiData);
      } else {
        // Crear nuevo KPI
        result = await createKpiSemanal(kpiData);
      }

      if (result.success) {
        await loadKpisSemanales(); // Recargar datos
        setIsKpiModalOpen(false);
        setEditingKpi(null); // Limpiar estado de edición
      } else {
        alert(result.error || 'Error al guardar los datos');
      }
    } catch (error) {
      console.error('Error al guardar KPI:', error);
      alert('Error al guardar los datos');
    }
  };

  // Función para editar un KPI existente
  const handleEditKpi = (kpi: any) => {
    const kpiData: KpiSemanalData = {
      id: kpi.id,
      year: kpi.year,
      weekNumber: kpi.weekNumber,
      weekStartDate: kpi.weekStartDate.toISOString ? kpi.weekStartDate.toISOString() : kpi.weekStartDate,
      weekEndDate: kpi.weekEndDate.toISOString ? kpi.weekEndDate.toISOString() : kpi.weekEndDate,
      volumenTotalLitros: kpi.volumenTotalLitros,
      crecimientoMensual: kpi.crecimientoMensual,
      margenBrutoPorLitro: kpi.margenBrutoPorLitro,
      tasaRetencionClientes: kpi.tasaRetencionClientes,
      cumplimientoObjetivo: kpi.cumplimientoObjetivo,
      desviacionVentas: kpi.desviacionVentas,
      cicloPromedioCierre: kpi.cicloPromedioCierre,
      clientesActivosMensuales: kpi.clientesActivosMensuales
    };

    setEditingKpi(kpiData);
    setIsKpiModalOpen(true);
  };

  // Función para eliminar un KPI
  const handleDeleteKpi = async (kpiId: string) => {
    // Buscar información del KPI para mostrar en el dialog
    const kpi = kpisSemanales.find(k => k.id === kpiId);
    const kpiInfo = kpi ? `Semana ${kpi.weekNumber}/${kpi.year}` : 'este KPI';

    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPI',
      message: `¿Estás seguro de que quieres eliminar el KPI de ${kpiInfo}? Esta acción no se puede deshacer.`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          const result = await deleteKpiSemanal(kpiId);

          if (result.success) {
            await loadKpisSemanales(); // Recargar datos
            setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
          } else {
            alert(result.error || 'Error al eliminar el KPI');
            setConfirmDialog(prev => ({ ...prev, loading: false }));
          }
        } catch (error) {
          console.error('Error al eliminar KPI:', error);
          alert('Error al eliminar el KPI');
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  };

  // Función para abrir modal de nuevo KPI
  const handleNewKpi = () => {
    setEditingKpi(null);
    setIsAddingOldWeek(false);
    setIsKpiModalOpen(true);
  };

  // Funciones para manejar KPIs de Compras
  const handleNewKpiCompras = () => {
    setEditingKpiCompras(null);
    setIsAddingOldWeekCompras(false);
    setIsKpiModalOpenCompras(true);
  };

  const handleSaveKpiCompras = async (data: KpiComprasData) => {
    try {
      let result;
      if (editingKpiCompras) {
        result = await updateKpiCompras(editingKpiCompras.id!, data);
      } else {
        result = await createKpiCompras(data);
      }

      if (result.success) {
        await loadKpisCompras(); // Recargar datos
        setIsKpiModalOpenCompras(false);
        setEditingKpiCompras(null); // Limpiar estado de edición
      } else {
        alert(result.error || 'Error al guardar los datos de compras');
      }
    } catch (error) {
      console.error("Error al guardar KPI de compras:", error);
      alert('Error al guardar los datos de compras');
    }
  };

  const loadKpisCompras = async () => {
    try {
      setLoadingKpisCompras(true);
      const result = await getKpisCompras({ limit: 50 }); // Cargar más datos para el historial
      if (result.success && result.data) {
        setKpisCompras(result.data);
      }
    } catch (error) {
      console.error("Error al cargar KPIs de compras:", error);
    } finally {
      setLoadingKpisCompras(false);
    }
  };

  // Funciones para manejar edición y eliminación de KPIs de compras
  const handleEditKpiCompras = (kpi: KpiComprasData) => {
    setEditingKpiCompras(kpi);
    setIsAddingOldWeekCompras(false);
    setIsKpiModalOpenCompras(true);
  };

  const handleDeleteKpiCompras = async (kpiId: string) => {
    try {
      const result = await deleteKpiCompras(kpiId);
      if (result.success) {
        await loadKpisCompras(); // Recargar datos
      } else {
        alert(result.error || 'Error al eliminar el KPI de compras');
      }
    } catch (error) {
      console.error("Error al eliminar KPI de compras:", error);
      alert('Error al eliminar el KPI de compras');
    }
  };

  // Función para agregar semana antigua
  const handleAddOldWeek = () => {
    setEditingKpi(null);
    setIsAddingOldWeek(true);
    setIsKpiModalOpen(true);
  };

  // Función para eliminar KPIs seleccionados
  const handleDeleteSelected = async () => {
    if (selectedKpis.length === 0) return;

    const confirmMessage = `¿Estás seguro de que quieres eliminar ${selectedKpis.length} KPI${selectedKpis.length > 1 ? 's' : ''}? Esta acción no se puede deshacer.`;

    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPIs Seleccionados',
      message: confirmMessage,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          // Eliminar cada KPI seleccionado
          for (const kpiId of selectedKpis) {
            const result = await deleteKpiSemanal(kpiId);
            if (!result.success) {
              throw new Error(result.error || 'Error al eliminar KPI');
            }
          }

          // Limpiar selección después de eliminar
          setSelectedKpis([]);

          // Refrescar datos
          await loadKpisSemanales();

          setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
        } catch (error) {
          console.error('Error al eliminar KPIs seleccionados:', error);
          alert('Error al eliminar algunos KPIs. Por favor, intenta de nuevo.');
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  };



  useEffect(() => {
    // Cargar datos reales desde la API
    const loadData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/admin/stats');
        if (response.ok) {
          const data = await response.json();
          setDashboardData({
            precios: data.tendenciaPrecios || [],
            terminales: data.ventasPorTerminal || [],
            leads: data.leadsConversion || [],
            tarifas: []
          });
          // Los datos de estadísticas generales se almacenan en dashboardData
          // Los KPIs semanales se manejan por separado en loadKpisSemanales()
        } else {
          console.error('Error al cargar estadísticas:', response.statusText);
        }
      } catch (error) {
        console.error('Error al cargar datos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
    loadKpisSemanales();
    loadKpisCompras();
  }, []);

  // Efecto para filtrar datos según el rango de tiempo seleccionado
  useEffect(() => {
    if (!loading && kpisSemanales.length > 0) {
      const now = new Date();
      let weeksToShow = 4;

      // Determinar cuántas semanas mostrar según el filtro
      if (timeRange === "4w") {
        weeksToShow = 4;
      } else if (timeRange === "12w") {
        weeksToShow = 12;
      } else if (timeRange === "24w") {
        weeksToShow = 24;
      }

      // Filtrar KPIs por el período seleccionado
      const filteredKpis = kpisSemanales.slice(0, weeksToShow);

      if (filteredKpis.length > 0) {
        // Actualizar indicadores con el promedio del período seleccionado
        const avgIndicadores = {
          volumenTotalLitros: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.volumenTotalLitros, 0) / filteredKpis.length),
          crecimientoMensual: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.crecimientoMensual, 0) / filteredKpis.length).toFixed(2)),
          margenBrutoPorLitro: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.margenBrutoPorLitro, 0) / filteredKpis.length).toFixed(2)),
          tasaRetencionClientes: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.tasaRetencionClientes, 0) / filteredKpis.length).toFixed(1)),
          cumplimientoObjetivo: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.cumplimientoObjetivo, 0) / filteredKpis.length).toFixed(1)),
          desviacionVentas: Number((filteredKpis.reduce((sum, kpi) => sum + kpi.desviacionVentas, 0) / filteredKpis.length).toFixed(2)),
          cicloPromedioCierre: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.cicloPromedioCierre, 0) / filteredKpis.length),
          clientesActivosMensuales: Math.round(filteredKpis.reduce((sum, kpi) => sum + kpi.clientesActivosMensuales, 0) / filteredKpis.length)
        };
        setIndicadoresActuales(avgIndicadores);

        // Actualizar evolución con los datos filtrados
        const evolutionData = filteredKpis.reverse().map((kpi: any) => ({
          mes: `S${kpi.weekNumber}`,
          volumen: kpi.volumenTotalLitros,
          crecimiento: kpi.crecimientoMensual,
          margen: kpi.margenBrutoPorLitro,
          retencion: kpi.tasaRetencionClientes,
          cumplimiento: kpi.cumplimientoObjetivo,
          desviacion: kpi.desviacionVentas,
          ciclo: kpi.cicloPromedioCierre,
          clientesActivos: kpi.clientesActivosMensuales
        }));
        setEvolucionActual(evolutionData);

        // Actualizar distribuciones basadas en los datos filtrados
        const excelentWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo >= 95).length;
        const goodWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo >= 80 && kpi.cumplimientoObjetivo < 95).length;
        const poorWeeks = filteredKpis.filter(kpi => kpi.cumplimientoObjetivo < 80).length;

        setDistribucionCumplimientoActual([
          { name: "Excelente (≥95%)", value: excelentWeeks, color: "#16a34a" },
          { name: "Bueno (80-94%)", value: goodWeeks, color: "#f97316" },
          { name: "Necesita Mejora (<80%)", value: poorWeeks, color: "#dc2626" }
        ]);

        const highRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes >= 90).length;
        const mediumRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes >= 75 && kpi.tasaRetencionClientes < 90).length;
        const lowRetention = filteredKpis.filter(kpi => kpi.tasaRetencionClientes < 75).length;

        setDistribucionRetencionActual([
          { name: "Alta (≥90%)", value: highRetention, color: "#16a34a" },
          { name: "Media (75-89%)", value: mediumRetention, color: "#f97316" },
          { name: "Baja (<75%)", value: lowRetention, color: "#dc2626" }
        ]);
      }
    }
  }, [timeRange, loading, kpisSemanales]);

  // Efecto para filtrar datos de compras según el rango de tiempo seleccionado
  useEffect(() => {
    if (!loading && kpisCompras.length > 0) {
      let weeksToShow = 4;

      // Determinar cuántas semanas mostrar según el filtro
      if (timeRangeCompras === "4w") {
        weeksToShow = 4;
      } else if (timeRangeCompras === "12w") {
        weeksToShow = 12;
      } else if (timeRangeCompras === "24w") {
        weeksToShow = 24;
      }

      // Filtrar KPIs de compras por el período seleccionado
      const filteredKpisCompras = kpisCompras.slice(0, weeksToShow);

      if (filteredKpisCompras.length > 0) {
        // Calcular distribución promedio de proveedores
        const proveedoresMap = new Map<string, { total: number, count: number, color: string }>();
        const colors = ["#3b82f6", "#10b981", "#f59e0b", "#8b5cf6", "#ef4444", "#06b6d4", "#8b5cf6", "#f97316"];

        filteredKpisCompras.forEach(kpi => {
          kpi.distribucionProveedores.forEach((proveedor: any, index: number) => {
            if (!proveedoresMap.has(proveedor.nombre)) {
              proveedoresMap.set(proveedor.nombre, {
                total: 0,
                count: 0,
                color: colors[index % colors.length]
              });
            }
            const existing = proveedoresMap.get(proveedor.nombre)!;
            existing.total += proveedor.porcentaje;
            existing.count += 1;
          });
        });

        const porcentajeCompraPorProveedor = Array.from(proveedoresMap.entries()).map(([nombre, data]) => ({
          proveedor: nombre,
          porcentaje: Number((data.total / data.count).toFixed(1)),
          color: data.color
        }));

        // Actualizar indicadores de compras con el promedio del período seleccionado
        const avgIndicadoresCompras = {
          numeroProveedoresActivos: Math.round(filteredKpisCompras.reduce((sum, kpi) => sum + kpi.numeroProveedoresActivos, 0) / filteredKpisCompras.length),
          porcentajeReporteGanancia: Number((filteredKpisCompras.reduce((sum, kpi) => sum + kpi.porcentajeReporteGanancia, 0) / filteredKpisCompras.length).toFixed(1)),
          preciosPromedioCompra: Number((filteredKpisCompras.reduce((sum, kpi) => sum + kpi.preciosPromedioCompra, 0) / filteredKpisCompras.length).toFixed(2)),
          diferencialPrecioPemex: Number((filteredKpisCompras.reduce((sum, kpi) => sum + kpi.diferencialPrecioPemex, 0) / filteredKpisCompras.length).toFixed(2)),
          porcentajeCompraPorProveedor
        };
        setIndicadoresComprasActuales(avgIndicadoresCompras);

        // Actualizar evolución de compras con los datos filtrados
        const evolutionDataCompras = filteredKpisCompras.reverse().map((kpi: any) => ({
          mes: `S${kpi.weekNumber}`,
          proveedores: kpi.numeroProveedoresActivos,
          reporteGanancia: kpi.porcentajeReporteGanancia,
          precioPromedio: kpi.preciosPromedioCompra,
          diferencial: kpi.diferencialPrecioPemex,
          distribucionProveedores: kpi.distribucionProveedores
        }));
        setEvolucionComprasActual(evolutionDataCompras);
      }
    } else if (!loading && kpisCompras.length === 0) {
      // Si no hay datos reales, mantener los datos mock
      setIndicadoresComprasActuales(mockData.indicadoresCompras);
      setEvolucionComprasActual(mockData.evolucionIndicadoresCompras);
    }
  }, [timeRangeCompras, loading, kpisCompras]);

  // Componente para el icono de información con tooltip
  const CalculationButton = memo(({ kpiId, calculation }: { kpiId: string; calculation: string }) => {
    const [isVisible, setIsVisible] = useState(false);

    const handleMouseEnter = useCallback(() => {
      setIsVisible(true);
    }, []);

    const handleMouseLeave = useCallback(() => {
      setIsVisible(false);
    }, []);

    return (
      <div className="relative inline-block">
        <div
          className="ml-2 p-1 cursor-default"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <Info className="h-3 w-3 text-gray-400" />
        </div>
        {isVisible && (
          <div className="absolute z-50 left-0 top-6 w-60 p-3 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="font-medium text-gray-600 text-xs mb-1 flex items-center">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></div>
              Información
            </div>
            <div className="text-xs text-gray-500 leading-relaxed">
              {calculation}
            </div>
            <div className="absolute -top-1 left-2 w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
          </div>
        )}
      </div>
    );
  });

  const StatCard = ({ title, value, icon: Icon, color, change }: any) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={`text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change > 0 ? '+' : ''}{change}% vs mes anterior
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );

  const tabs = [
    { id: "ventas", label: "Ventas", icon: ShoppingCart },
    { id: "compras", label: "Compras", icon: Package },
    { id: "logistica", label: "Logística", icon: Truck },
    { id: "contabilidad", label: "Contabilidad", icon: Calculator },
    { id: "operaciones", label: "Operaciones", icon: Settings },
    { id: "legal", label: "Legal", icon: Scale },
    { id: "planeacion", label: "Planeación", icon: Target },
    { id: "finanzas", label: "Finanzas", icon: Banknote },
  ];

  if (loading) {
    return (
      <div className="fixed top-0 left-0 w-full h-full bg-white flex items-center justify-center z-50">
        <Image
          alt="Logo Cassiopeia Petrolíferos | Combustibles Cassiopeia"
          width={355}
          src={appLogo}
          style={{ height: '10%', width: 'auto' }}
          priority
          loading="eager"
        />
      </div>
    );
  }

  return (
    <>
      {/* Admin Header */}
      <header className="link-color fixed top-0 inset-x-0 flex items-center z-40 bg-white w-full transition-all py-3.5">
        <div className="container">
          <nav className="flex items-center">
            <a href="/">
              <Image
                alt="Logo"
                loading="lazy"
                width={224}
                height={75}
                decoding="async"
                className=""
                src={appLogo}
                style={{ color: 'transparent' }}
              />
            </a>
            <div className="hidden lg:block mx-auto grow">
              <ul id="navbar-navlist" className="grow flex flex-col lg:flex-row lg:items-center lg:justify-center mt-4 lg:mt-0">
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-primary" href="/admin">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Layout / Layout-4-blocks</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect id="bound" x="0" y="0" width="24" height="24"></rect>
                          <rect id="Rectangle-7" fill="currentColor" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                          <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" id="Combined-Shape" fill="currentColor" opacity="0.3"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Dashboard</span>
                  </a>
                </li>
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-gray-700 hover:text-primary transition-all" href="/tarifas">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Shopping / Dollar</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect x="0" y="0" width="24" height="24"></rect>
                          <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 C19,3.55228475 18.5522847,4 18,4 L6,4 C5.44771525,4 5,3.55228475 5,3 C5,2.44771525 5.44771525,2 6,2 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M7.5,5 L16.5,5 C17.3284271,5 18,5.67157288 18,6.5 C18,7.32842712 17.3284271,8 16.5,8 L7.5,8 C6.67157288,8 6,7.32842712 6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M5,9 L19,9 C19.5522847,9 20,9.44771525 20,10 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,10 C4,9.44771525 4.44771525,9 5,9 Z M12,12 C10.8954305,12 10,12.8954305 10,14 C10,15.1045695 10.8954305,16 12,16 C13.1045695,16 14,15.1045695 14,14 C14,12.8954305 13.1045695,12 12,12 Z" fill="currentColor"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Tarifas</span>
                  </a>
                </li>
                <li className="nav-item pe-4">
                  <a className="nav-link flex items-center font-medium py-2 px-4 lg:py-0 text-gray-700 hover:text-primary transition-all" href="/admin/settings">
                    <span className="shrink-0 me-2">
                      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <title>Stockholm-icons / Shopping / Settings</title>
                        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                          <rect opacity="0.200000003" x="0" y="0" width="24" height="24"></rect>
                          <path d="M4.5,7 L9.5,7 C10.3284271,7 11,7.67157288 11,8.5 C11,9.32842712 10.3284271,10 9.5,10 L4.5,10 C3.67157288,10 3,9.32842712 3,8.5 C3,7.67157288 3.67157288,7 4.5,7 Z M13.5,15 L18.5,15 C19.3284271,15 20,15.6715729 20,16.5 C20,17.3284271 19.3284271,18 18.5,18 L13.5,18 C12.6715729,18 12,17.3284271 12,16.5 C12,15.6715729 12.6715729,15 13.5,15 Z" fill="currentColor" opacity="0.3"></path>
                          <path d="M17,11 C15.3431458,11 14,9.65685425 14,8 C14,6.34314575 15.3431458,5 17,5 C18.6568542,5 20,6.34314575 20,8 C20,9.65685425 18.6568542,11 17,11 Z M6,19 C4.34314575,19 3,17.6568542 3,16 C3,14.3431458 4.34314575,13 6,13 C7.65685425,13 9,14.3431458 9,16 C9,17.6568542 7.65685425,19 6,19 Z" fill="currentColor"></path>
                        </g>
                      </svg>
                    </span>
                    <span className="grow">Configuración</span>
                  </a>
                </li>
              </ul>
            </div>
            <div className="hidden lg:flex items-center ms-auto">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <div className="shrink">
                    <div className="h-8 w-8 me-2">
                      <div className="avatar h-full w-full rounded-full me-2 bg-primary/10 flex items-center justify-center">
                        <span className="text-primary font-medium text-sm">
                          {user.name ? user.name.charAt(0).toUpperCase() : (user.email ? user.email.charAt(0).toUpperCase() : 'U')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="grow ms-1 leading-normal">
                    <span className="block text-sm font-medium">{user.name || user.email}</span>
                    <span className="block text-gray-400 text-xs text-left">{user.role}</span>
                  </div>
                </div>
                <button
                  onClick={() => signOut({ callbackUrl: "/" })}
                  className="text-sm bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md transition-colors flex items-center"
                  title="Cerrar sesión"
                >
                  <span className="hidden lg:inline xl:hidden">Salir</span>
                  <span className="inline lg:hidden xl:inline">Cerrar sesión</span>
                </button>
              </div>
            </div>
            <div className="block grow ms-auto lg:shrink me-4 lg:me-0 lg:hidden">
              <ul className="navbar-nav flex gap-x-3 items-center justify-end lg:justify-center">
                <li className="nav-item">
                  <div className="relative">
                    <button className="nav-link after:absolute hover:after:-bottom-10 after:inset-0" type="button">
                      <div className="flex items-center">
                        <div className="shrink">
                          <div className="h-8 w-8 me-2">
                            <div className="avatar h-full w-full rounded-full me-2 bg-primary/10 flex items-center justify-center">
                              <span className="text-primary font-medium text-sm">
                                {user.name ? user.name.charAt(0).toUpperCase() : (user.email ? user.email.charAt(0).toUpperCase() : 'U')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>
                </li>
                <li className="nav-item">
                  <button
                    onClick={() => signOut({ callbackUrl: "/" })}
                    className="nav-link flex items-center font-medium py-2 px-3 text-gray-700 hover:text-red-600 transition-all"
                    title="Cerrar sesión"
                  >
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </li>
              </ul>
            </div>
            <div className="lg:hidden flex items-center ms-auto px-2.5">
              <button
                type="button"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"></path>
                </svg>
              </button>
            </div>
          </nav>
        </div>
      </header>

      <div className="bg-gradient-to-t from-orange-50/80 relative mt-[95px]">
      <div className="flex justify-center">
        <div className="flex max-w-7xl w-full">
          {/* Sidebar - Pegado al container pero fuera */}
          <div className={`
            lg:w-56 bg-white shadow-sm border border-gray-200 rounded-lg h-fit lg:ml-6 lg:mt-6 lg:mr-0
            ${sidebarOpen ? 'block' : 'hidden'} lg:block
            fixed lg:sticky lg:top-6 z-20 w-full lg:w-56
          `}>
          {/* Header del Sidebar */}
          <div className="p-4 border-b border-gray-100">
            <div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                user.role === "SUPER_ADMIN" ? "bg-red-100 text-red-800" :
                user.role === "WORKER" ? "bg-green-100 text-green-800" :
                "bg-blue-100 text-blue-800"
              }`}>
                {user.role}
              </span>
              <p className="text-sm text-gray-500 truncate mt-1">Bienvenido {user.name || user.email}</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-3" aria-label="Sidebar Navigation">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    className={`
                      text-start py-2 px-3 rounded-md transition-all w-full flex items-center gap-2
                      ${isActive
                        ? "bg-primary/10 text-primary"
                        : "bg-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      }
                    `}
                    onClick={() => {
                      setActiveTab(tab.id);
                      setSidebarOpen(false);
                    }}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`${tab.id}-panel`}
                  >
                    <tab.icon className={`h-4 w-4 ${isActive ? 'text-primary' : 'text-gray-500'}`} />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </nav>

        </div>

        {/* Overlay para móvil */}
        {sidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-15"
            onClick={() => setSidebarOpen(false)}
          />
        )}

          {/* Main Content - Con container que ocupa todo el espacio */}
          <div className="flex-1 min-w-0 lg:py-6 lg:pr-6 py-6 px-4">
            <div className="w-full lg:ml-0 mx-auto">
            {/* Mobile menu button */}
            <div className="lg:hidden bg-white shadow-lg border border-gray-200 p-4 rounded-lg mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Activity className="h-5 w-5 text-primary" />
                  </div>
                  <h2 className="text-xl font-medium text-gray-900">Panel Admin</h2>
                </div>
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                >
                  {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </button>
              </div>
            </div>
          {activeTab === "ventas" && (
            <div className="space-y-8">

              {/* Fila con título a la izquierda y selectores a la derecha */}
              <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
                <div className="mb-3 sm:mb-0">
                  <h1 className="text-2xl font-medium text-gray-900">
                    Panel de KPIs de Ventas
                  </h1>
                  <div className="flex items-center mt-1">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      hasRealData
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      <div className={`w-2 h-2 rounded-full mr-1 ${
                        hasRealData ? 'bg-green-500' : 'bg-yellow-500'
                      }`}></div>
                      {hasRealData ? 'Datos Reales Capturados' : 'Datos de Ejemplo'}
                    </div>
                    {hasRealData && kpisSemanales.length > 0 && (
                      <span className="ml-2 text-xs text-gray-500">
                        Última actualización: Semana {kpisSemanales[0].weekNumber}/{kpisSemanales[0].year}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRange === "4w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRange("4w")}
                    >
                      1m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRange === "12w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRange("12w")}
                    >
                      3m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRange === "24w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRange("24w")}
                    >
                      6m
                    </button>
                  </div>

                  {/* Botones de Gestión de KPIs */}
                  {user.role && (user.role === "ADMIN" || user.role === "SUPER_ADMIN" || user.role === "WORKER") && (
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1.5 lg:gap-2">
                      <button
                        type="button"
                        onClick={handleNewKpi}
                        className="p-2 text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                        title="Agregar Datos"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>

                      <button
                        type="button"
                        onClick={() => setShowKpiHistory(!showKpiHistory)}
                        className="p-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center"
                        title="Ver Historial"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>


                    </div>
                  )}
                </div>
              </div>

              {/* Primera Fila - 3 Columnas Compactas */}
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-3">
                {/* 1. Volumen Total de Venta por Mes */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                        <Droplet className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">VOLUMEN TOTAL DE VENTA (LITROS)</h3>
                          <CalculationButton
                            kpiId="volumen"
                            calculation="Suma total de litros vendidos en el mes"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {(indicadoresActuales.volumenTotalLitros / 1000).toLocaleString(undefined, { maximumFractionDigits: 1 })} K L
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Volumen (Litros)',
                            data: evolucionActual.map(item => item.volumen),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${(context.parsed.y / 1000).toFixed(1)}K Litros`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Volumen (Litros)'
                              },
                              ticks: {
                                callback: (value: any) => `${(value / 1000).toFixed(0)}K`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Volumen Total de Venta', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Volumen',
                            data: evolucionActual.map(item => item.volumen),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 0,
                            pointHoverRadius: 4
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${(context.parsed.y / 1000).toLocaleString(undefined, { maximumFractionDigits: 1 })} K L`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          },
                          elements: {
                            point: { radius: 0 }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 2. Crecimiento Mensual de Ventas */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center mr-3">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CRECIMIENTO DE VENTAS (%)</h3>
                          <CalculationButton
                            kpiId="crecimiento"
                            calculation="[(Ventas mes actual - Ventas mes anterior) / Ventas mes anterior] * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          +{indicadoresActuales.crecimientoMensual}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Crecimiento (%)',
                            data: evolucionActual.map(item => item.crecimiento),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y}% de crecimiento`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Crecimiento (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${value}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Crecimiento de Ventas', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Crecimiento',
                            data: evolucionActual.map(item => item.crecimiento),
                            borderColor: '#10b981',
                            backgroundColor: '#10b981',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#10b981'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 3. Margen Bruto por Litro Vendido */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-yellow-500/10 flex items-center justify-center mr-3">
                        <DollarSign className="h-5 w-5 text-yellow-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">MARGEN BRUTO POR LITRO VENDIDO</h3>
                          <CalculationButton
                            kpiId="margen"
                            calculation="(Ingreso total - Costo total) / Litros vendidos"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          ${indicadoresActuales.margenBrutoPorLitro}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Margen Bruto por Litro ($)',
                            data: evolucionActual.map(item => item.margen),
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `$${context.parsed.y} por litro`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Margen Bruto ($)'
                              },
                              ticks: {
                                callback: (value: any) => `$${value}`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Margen Bruto por Litro', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Margen',
                            data: evolucionActual.map(item => item.margen),
                            borderColor: '#f59e0b',
                            backgroundColor: '#f59e0b',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#f59e0b'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `$${context.parsed.y}`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Segunda Fila - 1 Columna Grande */}
              <div className="my-2">
                {/* 4. Tasa de Retención de Clientes - GRANDE */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center mr-3">
                          <Users className="h-5 w-5 text-purple-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">TASA DE RETENCIÓN DE CLIENTES</h3>
                            <CalculationButton
                              kpiId="retencion"
                              calculation="(Clientes retenidos / Clientes del periodo anterior) * 100"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.tasaRetencionClientes}%
                        </div>
                        <p className="text-xs text-gray-400">Tasa actual</p>
                      </div>
                    </div>
                  </div>
                  <div className="grid lg:grid-cols-2 gap-4">
                    <div
                      style={{ width: '100%', height: '150px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: distribucionRetencionActual.map(item => item.name),
                          datasets: [{
                            data: distribucionRetencionActual.map(item => item.value),
                            backgroundColor: distribucionRetencionActual.map(item => item.color),
                            borderWidth: 2,
                            borderColor: '#ffffff',
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '40%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.label}: ${context.parsed}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Distribución de la Tasa de Retención de Clientes', chartData, chartOptions, 'doughnut');
                      }}
                    >
                      <Doughnut
                        data={{
                          labels: distribucionRetencionActual.map(item => item.name),
                          datasets: [{
                            data: distribucionRetencionActual.map(item => item.value),
                            backgroundColor: distribucionRetencionActual.map(item => item.color),
                            borderWidth: 0
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '50%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed}%`
                              }
                            }
                          }
                        }}
                      />
                    </div>
                    <div
                      style={{ width: '100%', height: '150px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Tasa de Retención (%)',
                            data: evolucionActual.map(item => item.retencion),
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#8b5cf6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y}% de retención`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Tasa de Retención (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${value}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución de la Tasa de Retención de Clientes', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Retención',
                            data: evolucionActual.map(item => item.retencion),
                            borderColor: '#8b5cf6',
                            backgroundColor: '#8b5cf6',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            pointBackgroundColor: '#8b5cf6'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Tercera Fila - 3 Columnas Compactas */}
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-6">
                {/* 5. Porcentaje de Cumplimiento del Objetivo */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-indigo-500/10 flex items-center justify-center mr-3">
                        <Target className="h-5 w-5 text-indigo-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CUMPLIMIENTO DEL OBJETIVO DE VENTAS (%)</h3>
                          <CalculationButton
                            kpiId="cumplimiento"
                            calculation="(Ventas reales / Meta de ventas) * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.cumplimientoObjetivo}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: distribucionCumplimientoActual.map(item => item.name),
                          datasets: [{
                            data: distribucionCumplimientoActual.map(item => item.value),
                            backgroundColor: distribucionCumplimientoActual.map(item => item.color),
                            borderWidth: 2,
                            borderColor: '#ffffff',
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '50%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.label}: ${context.parsed}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Distribución del Cumplimiento de Objetivos', chartData, chartOptions, 'doughnut');
                      }}
                    >
                      <Doughnut
                        data={{
                          labels: distribucionCumplimientoActual.map(item => item.name),
                          datasets: [{
                            data: distribucionCumplimientoActual.map(item => item.value),
                            backgroundColor: distribucionCumplimientoActual.map(item => item.color),
                            borderWidth: 0
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1200,
                            easing: 'easeInOutQuart'
                          },
                          cutout: '60%',
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed}%`
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 6. Desviación entre Ventas Proyectadas y Reales */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-orange-500/10 flex items-center justify-center mr-3">
                        <Activity className="h-5 w-5 text-orange-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">DESVIACIÓN ENTRE VENTAS PROYECTADAS Y REALES</h3>
                          <CalculationButton
                            kpiId="desviacion"
                            calculation="(Ventas reales - Ventas proyectadas) / Ventas proyectadas * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.desviacionVentas}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Desviación (%)',
                            data: evolucionActual.map(item => item.desviacion),
                            borderColor: '#f97316',
                            backgroundColor: 'rgba(249, 115, 22, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f97316',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y}% de desviación`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Desviación (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${value}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución de la Desviación entre Ventas Proyectadas y Reales', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Desviación',
                            data: evolucionActual.map(item => item.desviacion),
                            borderColor: '#f97316',
                            backgroundColor: '#f97316',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#f97316'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 7. Ciclo Promedio de Cierre de Ventas */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-teal-500/10 flex items-center justify-center mr-3">
                        <Activity className="h-5 w-5 text-teal-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">CICLO PROMEDIO DE CIERRE DE VENTAS (DÍAS)</h3>
                          <CalculationButton
                            kpiId="ciclo"
                            calculation="Promedio de días entre contacto inicial y cierre"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.cicloPromedioCierre} días
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Ciclo Promedio de Cierre (días)',
                            data: evolucionActual.map(item => item.ciclo),
                            borderColor: '#14b8a6',
                            backgroundColor: 'rgba(20, 184, 166, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#14b8a6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y} días promedio`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Días Promedio'
                              },
                              ticks: {
                                callback: (value: any) => `${value} días`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Ciclo Promedio de Cierre de Ventas', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionActual.map(item => item.mes),
                          datasets: [{
                            label: 'Ciclo',
                            data: evolucionActual.map(item => item.ciclo),
                            borderColor: '#14b8a6',
                            backgroundColor: '#14b8a6',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#14b8a6'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y} días`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Cuarta Fila - 1 Columna Grande */}
              <div className="my-3">
                {/* 8. Número de Clientes Activos - GRANDE */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center mr-3">
                          <Users className="h-5 w-5 text-emerald-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">NÚMERO DE CLIENTES ACTIVOS</h3>
                            <CalculationButton
                              kpiId="clientesActivos"
                              calculation="Clientes con al menos una compra en el mes"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresActuales.clientesActivosMensuales.toLocaleString()}
                        </div>
                        <p className="text-xs text-gray-400">Clientes activos</p>
                      </div>
                    </div>
                  </div>
                  <div
                    style={{ width: '100%', height: '170px', cursor: 'pointer' }}
                    onClick={() => {
                      const chartData = {
                        labels: evolucionActual.map(item => item.mes),
                        datasets: [{
                          label: 'Clientes Activos',
                          data: evolucionActual.map(item => item.clientesActivos),
                          borderColor: '#10b981',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)',
                          borderWidth: 3,
                          fill: true,
                          tension: 0.4,
                          pointRadius: 5,
                          pointHoverRadius: 7,
                          pointBackgroundColor: '#10b981',
                          pointBorderColor: '#ffffff',
                          pointBorderWidth: 2
                        }]
                      };
                      const chartOptions = {
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          tooltip: {
                            callbacks: {
                              label: (context: any) => `${context.parsed.y.toLocaleString()} clientes`
                            }
                          }
                        },
                        scales: {
                          x: {
                            display: true,
                            title: {
                              display: true,
                              text: 'Período'
                            }
                          },
                          y: {
                            display: true,
                            title: {
                              display: true,
                              text: 'Número de Clientes'
                            },
                            ticks: {
                              callback: (value: any) => value.toLocaleString()
                            }
                          }
                        }
                      };
                      openChartDialog('Evolución del Número de Clientes Activos', chartData, chartOptions, 'line');
                    }}
                  >
                    <Line
                      data={{
                        labels: evolucionActual.map(item => item.mes),
                        datasets: [{
                          label: 'Clientes Activos',
                          data: evolucionActual.map(item => item.clientesActivos),
                          borderColor: '#10b981',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)',
                          borderWidth: 3,
                          fill: true,
                          tension: 0.4,
                          pointRadius: 3,
                          pointHoverRadius: 5,
                          pointBackgroundColor: '#10b981'
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          legend: { display: false },
                          tooltip: {
                            animation: {
                              duration: 200
                            },
                            callbacks: {
                              label: (context) => `${context.parsed.y.toLocaleString()}`
                            }
                          }
                        },
                        scales: {
                          x: { display: false },
                          y: { display: false }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
              {/* Dialog de Historial de KPIs */}
              {showKpiHistory && (
                <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                  <div className="w-full max-w-7xl">
                    {/* Overlay */}
                    <div
                      className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                      onClick={() => setShowKpiHistory(false)}
                    ></div>

                    {/* Dialog Content */}
                    <div className="relative bg-white rounded-xl shadow-2xl w-full max-h-[90vh] flex flex-col">
                      {/* Header del Dialog */}
                      <div className="bg-gray-50 px-6 pt-5 rounded-t-xl flex-shrink-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="flex items-center space-x-3">
                              <h2 className="text-xl font-medium text-gray-900">Historial de Semanas</h2>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {kpisSemanales.length} registros
                              </span>
                            </div>
                            <p className="text-sm text-gray-500">Tabla de rendimiento comercial y métricas semanales</p>
                          </div>

                          <div className="flex items-center space-x-4">
                            {/* Controles de selección del lado izquierdo */}
                            {selectedKpis.length > 0 && (
                              <div className="flex items-center space-x-3">
                                <span className="text-sm font-medium text-gray-900">
                                  {selectedKpis.length} seleccionado{selectedKpis.length > 1 ? 's' : ''}
                                </span>
                                {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                                  <button
                                    onClick={handleDeleteSelected}
                                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50 transition-colors"
                                    title="Eliminar elementos seleccionados"
                                  >
                                    <Trash2 className="h-3 w-3 mr-1" />
                                    Eliminar
                                  </button>
                                )}
                              </div>
                            )}

                            <div className="flex items-center space-x-2">
                              <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                              <button
                                onClick={() => {
                                  // Determinar qué datos exportar: seleccionados o todos
                                  const dataToExport = selectedKpis.length > 0
                                    ? kpisSemanales.filter(kpi => selectedKpis.includes(kpi.id))
                                    : kpisSemanales;

                                  const exportType = selectedKpis.length > 0 ? 'seleccionados' : 'todos';

                                  const headers = [
                                    'Semana', 'Año', 'Fecha Inicio', 'Fecha Fin', 'Volumen Total (L)',
                                    'Crecimiento (%)', 'Margen Bruto por Litro', 'Tasa Retención (%)',
                                    'Cumplimiento Objetivo (%)', 'Desviación Ventas (%)', 'Ciclo Promedio Cierre (días)',
                                    'Clientes Activos', 'Usuario', 'Fecha Creación'
                                  ];

                                  const csvContent = "data:text/csv;charset=utf-8," +
                                    headers.join(",") + "\n" +
                                    dataToExport.map(kpi => [
                                      kpi.weekNumber,
                                      kpi.year,
                                      new Date(kpi.weekStartDate).toLocaleDateString('es-ES'),
                                      new Date(kpi.weekEndDate).toLocaleDateString('es-ES'),
                                      kpi.volumenTotalLitros,
                                      kpi.crecimientoMensual,
                                      kpi.margenBrutoPorLitro,
                                      kpi.tasaRetencionClientes,
                                      kpi.cumplimientoObjetivo,
                                      kpi.desviacionVentas,
                                      kpi.cicloPromedioCierre,
                                      kpi.clientesActivosMensuales,
                                      `"${kpi.user?.name || kpi.user?.email || 'Usuario desconocido'}"`,
                                      kpi.createdAt ? new Date(kpi.createdAt).toLocaleDateString('es-ES') : ''
                                    ].join(",")).join("\n");

                                  const encodedUri = encodeURI(csvContent);
                                  const link = document.createElement("a");
                                  link.setAttribute("href", encodedUri);
                                  link.setAttribute("download", `kpis_${exportType}_${new Date().toISOString().split('T')[0]}.csv`);
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                                className="p-2 text-gray-500 hover:text-primary hover:bg-primary/5 rounded-md transition-all duration-200"
                                title={selectedKpis.length > 0 ? `Exportar ${selectedKpis.length} elementos seleccionados` : "Exportar todos los datos"}
                              >
                                <Download className="h-4 w-4" />
                              </button>
                              <div className="w-px h-6 bg-gray-200 mx-1"></div>
                              <button
                                onClick={() => setShowAnalytics(!showAnalytics)}
                                className={`p-2 rounded-md transition-all duration-200 ${
                                  showAnalytics
                                    ? 'text-primary bg-primary/10 shadow-sm'
                                    : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                                }`}
                                title="Mostrar/Ocultar Analytics"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                              <div className="w-px h-6 bg-gray-200 mx-1"></div>
                              <button
                                onClick={() => setShowFilters(!showFilters)}
                                className={`p-2 rounded-md transition-all duration-200 ${
                                  showFilters
                                    ? 'text-primary bg-primary/10 shadow-sm'
                                    : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                                }`}
                                title="Filtros"
                              >
                                <Filter className="h-4 w-4" />
                              </button>
                              {/* Divisor vertical - solo visible para ADMIN y SUPER_ADMIN */}
                              {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                              )}
                              {/* Botón Agregar Semana Antigua - solo visible para ADMIN y SUPER_ADMIN */}
                              {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                                <button
                                  onClick={handleAddOldWeek}
                                  className="p-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-all duration-200"
                                  title="Agregar Semana Antigua"
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              )}
                              </div>

                              {/* Botón de cerrar */}
                              <button
                                onClick={() => setShowKpiHistory(false)}
                                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                                title="Cerrar"
                              >
                                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Contenido del Dialog con scroll */}
                      <div className="flex-1 overflow-y-auto">
                        <KpiHistorySection
                          kpisSemanales={kpisSemanales}
                          loadingKpis={loadingKpis}
                          user={user}
                          onEditKpi={handleEditKpi}
                          onDeleteKpi={handleDeleteKpi}
                          onRefresh={loadKpisSemanales}
                          showAnalytics={showAnalytics}
                          showFilters={showFilters}
                          selectedKpis={selectedKpis}
                          onSelectionChange={setSelectedKpis}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          )}

          {activeTab === "compras" && (
            <div className="space-y-10">

              {/* Fila con título a la izquierda y selectores a la derecha */}
              <div className="flex flex-col sm:flex-row justify-between items-center">
                <div className="mb-3 sm:mb-0">
                  <h1 className="text-2xl font-medium text-gray-900">
                    Panel de KPIs de Compras
                  </h1>
                  <div className="flex items-center mt-1">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      kpisCompras.length > 0
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}>
                      <div className={`w-2 h-2 rounded-full mr-1 ${
                        kpisCompras.length > 0
                          ? "bg-green-500"
                          : "bg-yellow-500"
                      }`}></div>
                      {kpisCompras.length > 0 ? "Datos Reales Capturados" : "Datos de Ejemplo"}
                    </div>
                    {kpisCompras.length > 0 && (
                      <span className="ml-2 text-xs text-gray-500">
                        Última actualización: Semana {kpisCompras[0].weekNumber}/{kpisCompras[0].year}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeCompras === "4w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeCompras("4w")}
                    >
                      1m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeCompras === "12w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeCompras("12w")}
                    >
                      3m
                    </button>
                    <button
                      type="button"
                      className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                        timeRangeCompras === "24w"
                          ? "bg-primary text-white border-primary"
                          : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                      } rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                      onClick={() => setTimeRangeCompras("24w")}
                    >
                      6m
                    </button>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1.5 lg:gap-2">
                    <button
                      type="button"
                      onClick={handleNewKpiCompras}
                      className="p-2 text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                      title="Agregar Datos"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </button>

                    <button
                      type="button"
                      onClick={() => setShowKpiHistoryCompras(!showKpiHistoryCompras)}
                      className="p-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center"
                      title="Ver Historial"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* Primera Fila - 3 Columnas Compactas */}
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-2">
                {/* 1. Número de Proveedores Activos */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">NÚMERO DE PROVEEDORES ACTIVOS</h3>
                          <CalculationButton
                            kpiId="proveedores"
                            calculation="Proveedores con al menos una transacción en el mes"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresComprasActuales.numeroProveedoresActivos}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Número de Proveedores Activos',
                            data: evolucionComprasActual.map(item => item.proveedores),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y} proveedores activos`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Número de Proveedores'
                              },
                              ticks: {
                                stepSize: 1
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Número de Proveedores Activos', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Proveedores',
                            data: evolucionComprasActual.map(item => item.proveedores),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 0,
                            pointHoverRadius: 4
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y} proveedores`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          },
                          elements: {
                            point: { radius: 0 }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 2. Porcentaje de Reporte de Ganancia Operativa */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center mr-3">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PORCENTAJE DE REPORTE DE GANANCIA OPERATIVA</h3>
                          <CalculationButton
                            kpiId="reporteGanancia"
                            calculation="(Reportes de ganancia recibidos / Total de compras) * 100"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresComprasActuales.porcentajeReporteGanancia}%
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Porcentaje de Reporte de Ganancia (%)',
                            data: evolucionComprasActual.map(item => item.reporteGanancia),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `${context.parsed.y}% de reportes`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Porcentaje (%)'
                              },
                              ticks: {
                                callback: (value: any) => `${value}%`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución del Porcentaje de Reporte de Ganancia', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Reporte Ganancia',
                            data: evolucionComprasActual.map(item => item.reporteGanancia),
                            borderColor: '#10b981',
                            backgroundColor: '#10b981',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#10b981'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `${context.parsed.y}%`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* 3. Precios Promedios de Compra */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-yellow-500/10 flex items-center justify-center mr-3">
                        <DollarSign className="h-5 w-5 text-yellow-500" />
                      </div>
                      <div className="grow">
                        <div className="flex items-center">
                          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PRECIOS PROMEDIOS DE COMPRA</h3>
                          <CalculationButton
                            kpiId="precioPromedio"
                            calculation="Promedio ponderado de precios de compra por litro"
                          />
                        </div>
                        <div className="text-xl font-bold text-gray-900">
                          ${indicadoresComprasActuales.preciosPromedioCompra}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{ width: '100%', height: '80px', cursor: 'pointer' }}
                      onClick={() => {
                        const chartData = {
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Precio Promedio de Compra ($)',
                            data: evolucionComprasActual.map(item => item.precioPromedio),
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2
                          }]
                        };
                        const chartOptions = {
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            tooltip: {
                              callbacks: {
                                label: (context: any) => `$${context.parsed.y} por litro`
                              }
                            }
                          },
                          scales: {
                            x: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Período'
                              }
                            },
                            y: {
                              display: true,
                              title: {
                                display: true,
                                text: 'Precio Promedio ($)'
                              },
                              ticks: {
                                callback: (value: any) => `$${value}`
                              }
                            }
                          }
                        };
                        openChartDialog('Evolución de los Precios Promedios de Compra', chartData, chartOptions, 'line');
                      }}
                    >
                      <Line
                        data={{
                          labels: evolucionComprasActual.map(item => item.mes),
                          datasets: [{
                            label: 'Precio Promedio',
                            data: evolucionComprasActual.map(item => item.precioPromedio),
                            borderColor: '#f59e0b',
                            backgroundColor: '#f59e0b',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#f59e0b'
                          }]
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                          },
                          interaction: {
                            intersect: false,
                            mode: 'nearest'
                          },
                          plugins: {
                            legend: { display: false },
                            tooltip: {
                              animation: {
                                duration: 200
                              },
                              callbacks: {
                                label: (context) => `$${context.parsed.y}`
                              }
                            }
                          },
                          scales: {
                            x: { display: false },
                            y: { display: false }
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Segunda Fila - 1 Columna Grande */}
              <div className="my-2">
                {/* 4. Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX - GRANDE */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center mr-3">
                          <Activity className="h-5 w-5 text-purple-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">DIFERENCIAL ENTRE PRECIO DE COMPRA REAL Y PRECIO DE TERMINAL PEMEX</h3>
                            <CalculationButton
                              kpiId="diferencial"
                              calculation="Precio de compra real - Precio de terminal PEMEX"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {indicadoresComprasActuales.diferencialPrecioPemex}%
                        </div>
                        <p className="text-xs text-gray-400">Diferencial actual</p>
                      </div>
                    </div>
                  </div>
                  <div
                    style={{ width: '100%', height: '150px', cursor: 'pointer' }}
                    onClick={() => {
                      const chartData = {
                        labels: evolucionComprasActual.map(item => item.mes),
                        datasets: [{
                          label: 'Diferencial de Precio (%)',
                          data: evolucionComprasActual.map(item => item.diferencial),
                          borderColor: '#8b5cf6',
                          backgroundColor: 'rgba(139, 92, 246, 0.1)',
                          borderWidth: 3,
                          fill: true,
                          tension: 0.4,
                          pointRadius: 4,
                          pointHoverRadius: 6,
                          pointBackgroundColor: '#8b5cf6',
                          pointBorderColor: '#ffffff',
                          pointBorderWidth: 2
                        }]
                      };
                      const chartOptions = {
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          tooltip: {
                            callbacks: {
                              label: (context: any) => `${context.parsed.y}% de diferencial`
                            }
                          }
                        },
                        scales: {
                          x: {
                            display: true,
                            title: {
                              display: true,
                              text: 'Período'
                            }
                          },
                          y: {
                            display: true,
                            title: {
                              display: true,
                              text: 'Diferencial de Precio (%)'
                            },
                            ticks: {
                              callback: (value: any) => `${value}%`
                            }
                          }
                        }
                      };
                      openChartDialog('Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX', chartData, chartOptions, 'line');
                    }}
                  >
                    <Line
                      data={{
                        labels: evolucionComprasActual.map(item => item.mes),
                        datasets: [{
                          label: 'Diferencial',
                          data: evolucionComprasActual.map(item => item.diferencial),
                          borderColor: '#8b5cf6',
                          backgroundColor: '#8b5cf6',
                          borderWidth: 3,
                          fill: false,
                          tension: 0.4,
                          pointRadius: 3,
                          pointHoverRadius: 5,
                          pointBackgroundColor: '#8b5cf6'
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                          duration: 1000,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          legend: { display: false },
                          tooltip: {
                            animation: {
                              duration: 200
                            },
                            callbacks: {
                              label: (context) => `${context.parsed.y}%`
                            }
                          }
                        },
                        scales: {
                          x: { display: false },
                          y: { display: false }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Tercera Fila - 1 Columna Grande para el gráfico de pastel */}
              <div className="my-2">
                {/* 5. % Compra por Proveedor - GRANDE */}
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
                  <div className="relative">
                    <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center mr-3">
                          <Package className="h-5 w-5 text-emerald-500" />
                        </div>
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">% COMPRA POR PROVEEDOR</h3>
                            <CalculationButton
                              kpiId="compraPorProveedor"
                              calculation="Distribución porcentual de compras por proveedor"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    style={{ width: '100%', height: '300px', cursor: 'pointer' }}
                    onClick={() => {
                      const chartData = {
                        labels: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.proveedor),
                        datasets: [{
                          data: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.porcentaje),
                          backgroundColor: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.color),
                          borderWidth: 2,
                          borderColor: '#ffffff',
                          hoverBorderWidth: 3,
                          hoverBorderColor: '#ffffff'
                        }]
                      };
                      const chartOptions = {
                        animation: {
                          duration: 1200,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          tooltip: {
                            callbacks: {
                              label: (context: any) => `${context.label}: ${context.parsed}%`
                            }
                          }
                        }
                      };
                      openChartDialog('Distribución de Compras por Proveedor', chartData, chartOptions, 'pie');
                    }}
                  >
                    <Pie
                      data={{
                        labels: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.proveedor),
                        datasets: [{
                          data: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.porcentaje),
                          backgroundColor: indicadoresComprasActuales.porcentajeCompraPorProveedor.map(item => item.color),
                          borderWidth: 0
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                          duration: 1200,
                          easing: 'easeInOutQuart'
                        },
                        interaction: {
                          intersect: false,
                          mode: 'nearest'
                        },
                        plugins: {
                          legend: {
                            display: true,
                            position: 'right',
                            labels: {
                              usePointStyle: true,
                              padding: 20
                            }
                          },
                          tooltip: {
                            animation: {
                              duration: 200
                            },
                            callbacks: {
                              label: (context) => `${context.label}: ${context.parsed}%`
                            }
                          }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Dialog de Historial de KPIs de Compras */}
              {showKpiHistoryCompras && (
                <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                  <div className="w-full max-w-7xl">
                    {/* Overlay */}
                    <div
                      className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                      onClick={() => setShowKpiHistoryCompras(false)}
                    ></div>

                    {/* Dialog Content */}
                    <div className="relative bg-white rounded-xl shadow-2xl w-full max-h-[90vh] flex flex-col">
                      {/* Header del Dialog */}
                      <div className="bg-gray-50 px-6 pt-5 rounded-t-xl flex-shrink-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="flex items-center space-x-3">
                              <h2 className="text-xl font-medium text-gray-900">Historial de Semanas</h2>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {kpisCompras.length} registros
                              </span>
                            </div>
                            <p className="text-sm text-gray-500">Tabla de rendimiento de compras y métricas semanales</p>
                          </div>

                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                              <button
                                onClick={() => {
                                  // Exportar datos de compras
                                  const headers = [
                                    'Semana', 'Año', 'Fecha Inicio', 'Fecha Fin', 'Proveedores Activos',
                                    '% Reporte Ganancia', 'Precio Promedio Compra', 'Diferencial PEMEX (%)',
                                    'Distribución Proveedores', 'Usuario', 'Fecha Creación'
                                  ];

                                  const csvContent = "data:text/csv;charset=utf-8," +
                                    headers.join(",") + "\n" +
                                    kpisCompras.map(kpi => [
                                      kpi.weekNumber,
                                      kpi.year,
                                      new Date(kpi.weekStartDate).toLocaleDateString('es-ES'),
                                      new Date(kpi.weekEndDate).toLocaleDateString('es-ES'),
                                      kpi.numeroProveedoresActivos,
                                      kpi.porcentajeReporteGanancia,
                                      kpi.preciosPromedioCompra,
                                      kpi.diferencialPrecioPemex,
                                      `"${kpi.distribucionProveedores.map((p: any) => `${p.nombre}: ${p.porcentaje}%`).join('; ')}"`,
                                      `"${kpi.user?.name || kpi.user?.email || 'Usuario desconocido'}"`,
                                      kpi.createdAt ? new Date(kpi.createdAt).toLocaleDateString('es-ES') : ''
                                    ].join(",")).join("\n");

                                  const encodedUri = encodeURI(csvContent);
                                  const link = document.createElement("a");
                                  link.setAttribute("href", encodedUri);
                                  link.setAttribute("download", `kpis_compras_${new Date().toISOString().split('T')[0]}.csv`);
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                                className="p-2 text-gray-500 hover:text-primary hover:bg-primary/5 rounded-md transition-all duration-200"
                                title="Exportar todos los datos"
                              >
                                <Download className="h-4 w-4" />
                              </button>
                              <div className="w-px h-6 bg-gray-200 mx-1"></div>
                              <button
                                onClick={() => setShowFiltersCompras(!showFiltersCompras)}
                                className={`p-2 rounded-md transition-all duration-200 ${
                                  showFiltersCompras
                                    ? 'text-primary bg-primary/10 shadow-sm'
                                    : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                                }`}
                                title="Filtros"
                              >
                                <Filter className="h-4 w-4" />
                              </button>
                              {/* Divisor vertical - solo visible para ADMIN y SUPER_ADMIN */}
                              {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                              )}
                              {/* Botón Agregar Semana Antigua - solo visible para ADMIN y SUPER_ADMIN */}
                              {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                                <button
                                  onClick={() => {
                                    setEditingKpiCompras(null);
                                    setIsAddingOldWeekCompras(true);
                                    setIsKpiModalOpenCompras(true);
                                    // NO cerrar el historial - mantener setShowKpiHistoryCompras(true)
                                  }}
                                  className="p-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-all duration-200"
                                  title="Agregar Semana Antigua"
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              )}
                              </div>

                              {/* Botón de cerrar */}
                              <button
                                onClick={() => setShowKpiHistoryCompras(false)}
                                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                                title="Cerrar"
                              >
                                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Contenido del Dialog con scroll */}
                      <div className="flex-1 overflow-y-auto">
                        <KpiComprasHistorySection
                          kpisCompras={kpisCompras}
                          loadingKpis={loadingKpisCompras}
                          user={user}
                          onEditKpi={handleEditKpiCompras}
                          onDeleteKpi={handleDeleteKpiCompras}
                          onRefresh={loadKpisCompras}
                          showAnalytics={showAnalyticsCompras}
                          showFilters={showFiltersCompras}
                          selectedKpis={selectedKpisCompras}
                          onSelectionChange={setSelectedKpisCompras}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "logistica" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión de Transporte
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Logística
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de logística y optimización de transporte
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-orange-500/10 flex items-center justify-center mb-4">
                  <Truck className="h-6 w-6 text-orange-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Logística</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de logística en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "contabilidad" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Financiera
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Contabilidad
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración contable y control financiero
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-green-500/10 flex items-center justify-center mb-4">
                  <Calculator className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Contabilidad</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de contabilidad en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "operaciones" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Operativa
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Operaciones
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de operaciones diarias y procesos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-purple-500/10 flex items-center justify-center mb-4">
                  <Settings className="h-6 w-6 text-purple-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Operaciones</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de operaciones en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "legal" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Jurídica
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control Legal
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de asuntos legales y cumplimiento normativo
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-red-500/10 flex items-center justify-center mb-4">
                  <Scale className="h-6 w-6 text-red-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel Legal</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad legal en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "planeacion" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Estratégica
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Planeación
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración de planeación estratégica y proyectos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-indigo-500/10 flex items-center justify-center mb-4">
                  <Target className="h-6 w-6 text-indigo-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Planeación</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de planeación en desarrollo...</p>
              </div>
            </div>
          )}

          {activeTab === "finanzas" && (
            <div className="space-y-10">
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 relative">
                <div className="relative">
                  <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
                  <div className="text-center">
                    <span className="text-xs md:text-xs lg:text-sm font-medium py-1 px-3 rounded-full text-primary bg-primary/10">
                      Gestión Financiera
                    </span>
                    <h1 className="text-2xl md:text-2xl lg:text-3xl font-medium mt-3 mb-4 max-w-2xl justify-center mx-auto">
                      Panel de Control de Finanzas
                    </h1>
                    <p className="text-base md:text-base lg:text-xl text-gray-500">
                      Administración financiera y control de presupuestos
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div className="h-12 w-12 rounded-md bg-emerald-500/10 flex items-center justify-center mb-4">
                  <Banknote className="h-6 w-6 text-emerald-500" />
                </div>
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-4">Panel de Finanzas</h3>
                <p className="text-base md:text-base lg:text-lg text-gray-500">Funcionalidad de finanzas en desarrollo...</p>
              </div>
            </div>
          )}
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Modal para captura de KPIs semanales */}
      <KpiSemanalModal
        isOpen={isKpiModalOpen}
        onClose={() => {
          setIsKpiModalOpen(false);
          setEditingKpi(null);
          setIsAddingOldWeek(false);
        }}
        onSave={handleSaveKpi}
        editingKpi={editingKpi}
        isAddingOldWeek={isAddingOldWeek}
        existingKpis={kpisSemanales}
      />

      {/* Modal para captura de KPIs de compras */}
      <KpiComprasModal
        isOpen={isKpiModalOpenCompras}
        onClose={() => {
          setIsKpiModalOpenCompras(false);
          setEditingKpiCompras(null);
          setIsAddingOldWeekCompras(false);
        }}
        onSave={handleSaveKpiCompras}
        editingKpi={editingKpiCompras}
        isAddingOldWeek={isAddingOldWeekCompras}
        existingKpis={kpisCompras}
      />

      {/* Dialog para mostrar gráficas en pantalla completa */}
      {chartDialog.isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-[60rem] max-h-[85vh] mx-auto">
            {/* Overlay */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={() => setChartDialog(prev => ({ ...prev, isOpen: false }))}
            ></div>

            {/* Dialog Content */}
            <div className="relative bg-white rounded-xl shadow-2xl w-full h-auto flex flex-col">
              {/* Header del Dialog */}
              <div className="bg-white px-6 pt-4 rounded-t-xl flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-medium text-gray-600">{chartDialog.title}</h2>
                  </div>
                  <button
                    onClick={() => setChartDialog(prev => ({ ...prev, isOpen: false }))}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                    title="Cerrar"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Contenido del gráfico */}
              <div className="px-6 py-3">
                <div className="w-full h-[500px]">
                  {chartDialog.chartType === 'line' && (
                    <Line
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                  {chartDialog.chartType === 'pie' && (
                    <Pie
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                  {chartDialog.chartType === 'doughnut' && (
                    <Doughnut
                      data={chartDialog.chartData}
                      options={chartDialog.chartOptions}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Dialog de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
        loading={confirmDialog.loading}
      />
    </>
  );
};

export default AdminDashboard;
