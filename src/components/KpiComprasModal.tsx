"use client";

import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Plus, Trash2 } from "lucide-react";
import { getCurrentWeekInfo, getWeekInfo, type WeekInfo } from "@/lib/utils/weekUtils";
import { type KpiComprasData, type ProveedorData } from "@/app/actions/kpis-compras";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import AlertDialog from "@/components/ui/AlertDialog";

// Interfaz para el formulario que permite valores string o number
interface FormKpiComprasData extends Omit<KpiComprasData, 'numeroProveedoresActivos' | 'porcentajeReporteGanancia' | 'preciosPromedioCompra' | 'diferencialPrecioPemex' | 'distribucionProveedores'> {
  numeroProveedoresActivos: number | string;
  porcentajeReporteGanancia: number | string;
  preciosPromedioCompra: number | string;
  diferencialPrecioPemex: number | string;
  distribucionProveedores: ProveedorData[];
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80; // altura estimada más realista

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280; // ancho estimado más realista
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20; // margen de seguridad

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < margin) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-0 h-0";

    if (position.top) {
      classes += " bottom-full";
    } else {
      classes += " top-full";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    const baseStyle = {
      borderLeft: '6px solid transparent',
      borderRight: '6px solid transparent',
    };

    if (position.top) {
      return {
        ...baseStyle,
        borderBottom: '6px solid rgba(0, 0, 0, 0.8)'
      };
    } else {
      return {
        ...baseStyle,
        borderTop: '6px solid rgba(0, 0, 0, 0.8)'
      };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '280px',
            width: 'max-content',
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

interface KpiComprasModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: KpiComprasData) => Promise<void>;
  editingKpi?: KpiComprasData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: KpiComprasData[];
}

const KpiComprasModal: React.FC<KpiComprasModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = []
}) => {
  const [loading, setLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState<WeekInfo | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);
  const [showInvalidDataAlert, setShowInvalidDataAlert] = useState(false);
  const [invalidDataMessage, setInvalidDataMessage] = useState("");
  const [formData, setFormData] = useState<FormKpiComprasData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    numeroProveedoresActivos: "" as any,
    porcentajeReporteGanancia: "" as any,
    preciosPromedioCompra: "" as any,
    diferencialPrecioPemex: "" as any,
    distribucionProveedores: [
      { nombre: "Proveedor A", porcentaje: 0 },
      { nombre: "Proveedor B", porcentaje: 0 },
      { nombre: "Proveedor C", porcentaje: 0 }
    ]
  });

  useEffect(() => {
    if (isOpen) {
      if (editingKpi) {
        setFormData(editingKpi);
        const weekInfo = getWeekInfo(editingKpi.year, editingKpi.weekNumber);
        setCurrentWeek(weekInfo);
        setSelectedYear(editingKpi.year);
        setSelectedWeek(editingKpi.weekNumber);
      } else if (isAddingOldWeek) {
        // Para semanas antiguas, inicializar con valores por defecto pero permitir selección
        const currentYear = new Date().getFullYear();
        setSelectedYear(currentYear);
        setSelectedWeek(1);
        try {
          const weekInfo = getWeekInfo(currentYear, 1);
          setCurrentWeek(weekInfo);
          setFormData({
            year: currentYear,
            weekNumber: 1,
            weekStartDate: weekInfo.startDate.toISOString(),
            weekEndDate: weekInfo.endDate.toISOString(),
            numeroProveedoresActivos: "" as any,
            porcentajeReporteGanancia: "" as any,
            preciosPromedioCompra: "" as any,
            diferencialPrecioPemex: "" as any,
            distribucionProveedores: [
              { nombre: "Proveedor A", porcentaje: 0 },
              { nombre: "Proveedor B", porcentaje: 0 },
              { nombre: "Proveedor C", porcentaje: 0 }
            ]
          });
        } catch (error) {
          console.error("Error al inicializar semana antigua:", error);
        }
      } else {
        // Para semana actual
        try {
          const weekInfo = getCurrentWeekInfo();
          setCurrentWeek(weekInfo);
          setSelectedYear(weekInfo.year);
          setSelectedWeek(weekInfo.weekNumber);
          setFormData({
            year: weekInfo.year,
            weekNumber: weekInfo.weekNumber,
            weekStartDate: weekInfo.startDate.toISOString(),
            weekEndDate: weekInfo.endDate.toISOString(),
            numeroProveedoresActivos: "" as any,
            porcentajeReporteGanancia: "" as any,
            preciosPromedioCompra: "" as any,
            diferencialPrecioPemex: "" as any,
            distribucionProveedores: [
              { nombre: "Proveedor A", porcentaje: 0 },
              { nombre: "Proveedor B", porcentaje: 0 },
              { nombre: "Proveedor C", porcentaje: 0 }
            ]
          });
        } catch (error) {
          console.error("Error al inicializar semana actual:", error);
        }
      }
    }
  }, [isOpen, editingKpi, isAddingOldWeek]);

  const handleInputChange = (field: keyof FormKpiComprasData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Funciones para manejar proveedores dinámicos
  const handleProveedorChange = (index: number, field: 'nombre' | 'porcentaje', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      distribucionProveedores: prev.distribucionProveedores.map((proveedor, i) =>
        i === index
          ? { ...proveedor, [field]: field === 'porcentaje' ? Number(value) : value }
          : proveedor
      )
    }));
  };

  const addProveedor = () => {
    setFormData(prev => ({
      ...prev,
      distribucionProveedores: [
        ...prev.distribucionProveedores,
        { nombre: `Proveedor ${String.fromCharCode(65 + prev.distribucionProveedores.length)}`, porcentaje: 0 }
      ]
    }));
  };

  const removeProveedor = (index: number) => {
    if (formData.distribucionProveedores.length > 1) {
      setFormData(prev => ({
        ...prev,
        distribucionProveedores: prev.distribucionProveedores.filter((_, i) => i !== index)
      }));
    }
  };

  // Calcular el total de porcentajes
  const totalPorcentaje = formData.distribucionProveedores.reduce((sum, proveedor) => sum + proveedor.porcentaje, 0);

  // Función para manejar cambio de año
  const handleYearChange = (year: number) => {
    if (isNaN(year) || year < 1900 || year > 2100) {
      console.error("Año inválido:", year);
      return;
    }

    setSelectedYear(year);
    try {
      const weekInfo = getWeekInfo(year, selectedWeek);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      const fallbackWeekInfo = getWeekInfo(year, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para manejar cambio de semana
  const handleWeekChange = (week: number) => {
    if (isNaN(week) || week < 1 || week > 53) {
      console.error("Número de semana inválido:", week);
      return;
    }

    setSelectedWeek(week);
    try {
      const weekInfo = getWeekInfo(selectedYear, week);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: week,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      const fallbackWeekInfo = getWeekInfo(selectedYear, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para verificar si ya existe un KPI para la semana seleccionada
  const checkDuplicateKpi = (year: number, weekNumber: number): boolean => {
    if (editingKpi) return false; // Si estamos editando, no verificar duplicados

    return existingKpis.some(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Verificar si ya existe un KPI para esta semana
    if (checkDuplicateKpi(formData.year, formData.weekNumber)) {
      setShowDuplicateAlert(true);
      return;
    }

    // Validar que la suma de porcentajes sea 100%
    if (Math.abs(totalPorcentaje - 100) > 0.1) {
      setInvalidDataMessage("La suma de porcentajes de proveedores debe ser exactamente 100%");
      setShowInvalidDataAlert(true);
      return;
    }

    setLoading(true);

    try {
      // Convertir valores vacíos a 0 antes de enviar
      const processedData: KpiComprasData = {
        ...formData,
        numeroProveedoresActivos: formData.numeroProveedoresActivos === "" || formData.numeroProveedoresActivos === null ? 0 : Number(formData.numeroProveedoresActivos),
        porcentajeReporteGanancia: formData.porcentajeReporteGanancia === "" || formData.porcentajeReporteGanancia === null ? 0 : Number(formData.porcentajeReporteGanancia),
        preciosPromedioCompra: formData.preciosPromedioCompra === "" || formData.preciosPromedioCompra === null ? 0 : Number(formData.preciosPromedioCompra),
        diferencialPrecioPemex: formData.diferencialPrecioPemex === "" || formData.diferencialPrecioPemex === null ? 0 : Number(formData.diferencialPrecioPemex),
        distribucionProveedores: formData.distribucionProveedores
      };

      await onSave(processedData);
      onClose();
    } catch (error) {
      console.error("Error al guardar KPI de compras:", error);
      setInvalidDataMessage("Error al guardar los datos. Por favor, verifica la información e intenta nuevamente.");
      setShowInvalidDataAlert(true);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const kpiFields = [
    {
      key: "numeroProveedoresActivos" as keyof FormKpiComprasData,
      title: "NÚMERO DE PROVEEDORES ACTIVOS",
      label: "Número de Proveedores Activos",
      description: "Proveedores con al menos una transacción en el mes",
      type: "number",
      min: 0,
      max: 1000,
      step: 1
    },
    {
      key: "porcentajeReporteGanancia" as keyof FormKpiComprasData,
      title: "PORCENTAJE DE REPORTE DE GANANCIA OPERATIVA (%)",
      label: "Porcentaje de Reporte de Ganancia Operativa (%)",
      description: "(Reportes de ganancia recibidos / Total de compras) * 100",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "preciosPromedioCompra" as keyof FormKpiComprasData,
      title: "PRECIOS PROMEDIOS DE COMPRA",
      label: "Precios Promedios de Compra",
      description: "Promedio ponderado de precios de compra por litro",
      type: "number",
      min: 0,
      max: 100,
      step: 0.01
    },
    {
      key: "diferencialPrecioPemex" as keyof FormKpiComprasData,
      title: "DIFERENCIAL PRECIO PEMEX",
      label: "Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX",
      description: "Precio de compra real - Precio de terminal PEMEX",
      type: "number",
      min: -100,
      max: 100,
      step: 0.01
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-50 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-5 flex-shrink-0 rounded-t-lg">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {editingKpi ? "Editar" : isAddingOldWeek ? "Agregar Semana Antigua" : "Agregar"} Datos de Compras
            </h2>
            {currentWeek && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                {currentWeek.label}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <div className="p-6 space-y-6 overflow-y-auto flex-1">
            {/* Selectores de Año y Semana para semanas antiguas */}
            {isAddingOldWeek && !editingKpi && (
              <div className="bg-blue-100 rounded-lg p-3">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Seleccionar Semana</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex flex-col h-full">
                    <label className="block text-sm font-medium text-blue-800 mb-1">
                      Año
                    </label>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => {
                        const year = parseInt(value);
                        if (!isNaN(year)) {
                          handleYearChange(year);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                        <SelectValue placeholder="Seleccione un año" />
                      </SelectTrigger>
                      <SelectContent
                        position="popper"
                        side="bottom"
                        align="start"
                        sideOffset={4}
                        className="z-[9999] max-h-[200px] overflow-y-auto"
                      >
                        {Array.from({ length: 5 }, (_, i) => {
                          const year = new Date().getFullYear() - i;
                          return (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col h-full">
                    <label className="block text-sm font-medium text-blue-800 mb-1">
                      Semana
                    </label>
                    <Select
                      value={selectedWeek.toString()}
                      onValueChange={(value) => {
                        const week = parseInt(value);
                        if (!isNaN(week)) {
                          handleWeekChange(week);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                        <SelectValue placeholder="Seleccione una semana" />
                      </SelectTrigger>
                      <SelectContent
                        position="popper"
                        side="bottom"
                        align="start"
                        sideOffset={4}
                        className="z-[9999] max-h-[200px] overflow-y-auto"
                      >
                        {Array.from({ length: 52 }, (_, i) => {
                          const week = i + 1;
                          return (
                            <SelectItem key={week} value={week.toString()}>
                              Semana {week}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {currentWeek && (
                  <div className="mt-2 flex items-center gap-2">
                    <span className="text-sm font-medium text-blue-800">Período:</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                      {currentWeek.label}
                    </span>
                  </div>
                )}
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {kpiFields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <div className="space-y-1">
                    <Tooltip content={field.description}>
                      <h3 className="text-sm font-bold text-gray-600 uppercase tracking-wide cursor-default">
                        {field.title}
                      </h3>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    inputMode="decimal"
                    pattern="[0-9]*\.?[0-9]*"
                    placeholder="Ingrese un valor"
                    value={formData[field.key]}
                    onChange={(e) => handleInputChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 placeholder-gray-400"
                    required
                  />
                </div>
              ))}
            </div>

            {/* Sección de Distribución por Proveedores */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Tooltip content="Distribución porcentual de compras por proveedor">
                    <h3 className="text-sm font-bold text-gray-600 uppercase tracking-wide cursor-default">
                      % COMPRA POR PROVEEDOR
                    </h3>
                  </Tooltip>
                  <p className="text-xs">
                    Total: <span className="text-black font-medium">{totalPorcentaje.toFixed(1)}%</span>
                    {totalPorcentaje !== 100 && (
                      <span className="ml-1">
                         (Debe Sumar 100%)
                      </span>
                    )}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={addProveedor}
                  className="inline-flex gap-2 items-center border border-primary text-primary rounded-md focus:shadow-none focus:outline focus:outline-primary/40 transition-all duration-500 py-2 px-4 text-xs font-medium"
                >
                  <Plus className="h-4 w-4" />
                  Agregar Proveedor
                </button>
              </div>

              <div className="space-y-2">
                {formData.distribucionProveedores.map((proveedor, index) => (
                  <div key={index} className="flex items-center gap-3 pt-3 pb-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="Nombre del proveedor"
                        value={proveedor.nombre}
                        onChange={(e) => handleProveedorChange(index, 'nombre', e.target.value)}
                        maxLength={50}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                      />
                    </div>
                    <div className="w-24">
                      <input
                        type="text"
                        inputMode="decimal"
                        pattern="[0-9]*\.?[0-9]*"
                        placeholder="0"
                        value={proveedor.porcentaje || ''}
                        onChange={(e) => handleProveedorChange(index, 'porcentaje', e.target.value)}
                        maxLength={5}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent text-center"
                        required
                      />
                    </div>
                    <span className="text-sm text-gray-500">%</span>
                    {formData.distribucionProveedores.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeProveedor(index)}
                        className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                        title="Eliminar proveedor"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 px-6 py-3 bg-gray-50 flex-shrink-0 rounded-b-lg">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{loading ? "Guardando..." : "Guardar"}</span>
            </button>
          </div>
        </form>
      </div>

      {/* Alert Dialog para KPI duplicado */}
      <AlertDialog
        isOpen={showDuplicateAlert}
        onClose={() => setShowDuplicateAlert(false)}
        title="Ya existe un KPI para esta semana"
        message={`Ya existe un registro de KPI de compras para la semana ${formData.weekNumber}/${formData.year}. Por favor, selecciona una semana diferente o edita el registro existente.`}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para datos inválidos */}
      <AlertDialog
        isOpen={showInvalidDataAlert}
        onClose={() => setShowInvalidDataAlert(false)}
        title="Datos inválidos"
        message={invalidDataMessage}
        type="error"
        buttonText="Corregir"
      />
    </div>
  );
};

export default KpiComprasModal;
