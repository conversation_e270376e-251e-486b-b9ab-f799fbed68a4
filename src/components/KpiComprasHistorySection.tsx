"use client";

import React, { useState, useMemo, memo, useCallback } from "react";
import { Search, BarChart3, Edit, Trash2, Eye, EyeOff, Filter, Download, ChevronLeft, ChevronRight, ArrowUp, ArrowDown, ChevronDown } from "lucide-react";
import { type KpiComprasData } from "@/app/actions/kpis-compras";
import ConfirmDialog from "@/components/ui/ConfirmDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role?: string;
}

interface KpiComprasHistorySectionProps {
  kpisCompras: KpiComprasData[];
  loadingKpis: boolean;
  user: User;
  onEditKpi: (kpi: KpiComprasData) => void;
  onDeleteKpi: (kpiId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  showAnalytics?: boolean;
  showFilters?: boolean;
  selectedKpis?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80;

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280;
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20;

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < margin) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-0 h-0";

    if (position.top) {
      classes += " bottom-full";
    } else {
      classes += " top-full";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    const baseStyle = {
      borderLeft: '6px solid transparent',
      borderRight: '6px solid transparent',
    };

    if (position.top) {
      return {
        ...baseStyle,
        borderBottom: '6px solid rgba(0, 0, 0, 0.8)'
      };
    } else {
      return {
        ...baseStyle,
        borderTop: '6px solid rgba(0, 0, 0, 0.8)'
      };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '200px',
            width: 'max-content',
            whiteSpace: 'pre-line',
            wordWrap: 'break-word',
            lineHeight: '1.3',
            maxHeight: '150px',
            overflowY: 'visible'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

const KpiComprasHistorySection: React.FC<KpiComprasHistorySectionProps> = ({
  kpisCompras,
  loadingKpis,
  user,
  onEditKpi,
  onDeleteKpi,
  onRefresh,
  showAnalytics: externalShowAnalytics = false,
  showFilters: externalShowFilters = false,
  selectedKpis: externalSelectedKpis = [],
  onSelectionChange
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState<number | "all">("all");
  const [sortField, setSortField] = useState<keyof KpiComprasData>("weekNumber");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedKpis, setSelectedKpis] = useState<string[]>(externalSelectedKpis);
  const [showAnalytics, setShowAnalytics] = useState(externalShowAnalytics);
  const [showFilters, setShowFilters] = useState(externalShowFilters);
  const [openDropdowns, setOpenDropdowns] = useState<Set<string>>(new Set());
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    loading: false
  });

  // Sincronizar con props externas
  React.useEffect(() => {
    setSelectedKpis(externalSelectedKpis);
  }, [externalSelectedKpis]);

  React.useEffect(() => {
    setShowAnalytics(externalShowAnalytics);
  }, [externalShowAnalytics]);

  React.useEffect(() => {
    setShowFilters(externalShowFilters);
  }, [externalShowFilters]);

  // Cerrar dropdowns cuando se hace click fuera
  React.useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdowns(new Set());
    };

    if (openDropdowns.size > 0) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [openDropdowns]);

  // Función para formatear fechas
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: '2-digit', 
      month: '2-digit'
    });
  };

  // Filtrar y ordenar datos
  const filteredAndSortedKpis = useMemo(() => {
    let filtered = kpisCompras.filter((kpi: any) => {
      // Filtro de búsqueda: busca en semana/año y nombre/email del usuario
      const matchesSearch = searchTerm === "" ||
        `${kpi.weekNumber}/${kpi.year}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `semana ${kpi.weekNumber}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (kpi.user?.name?.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (kpi.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()));

      // Filtro por año
      const matchesYear = selectedYear === "all" || kpi.year === selectedYear;

      return matchesSearch && matchesYear;
    });

    // Ordenar
    filtered.sort((a: any, b: any) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Manejar casos especiales
      if (sortField === "weekStartDate" || sortField === "weekEndDate") {
        aValue = new Date(aValue as string).getTime();
        bValue = new Date(bValue as string).getTime();
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [kpisCompras, searchTerm, selectedYear, sortField, sortDirection]);

  // Paginación
  const totalPages = Math.ceil(filteredAndSortedKpis.length / itemsPerPage);
  const paginatedKpis = filteredAndSortedKpis.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Obtener años únicos para el filtro
  const availableYears = useMemo(() => {
    const years = [...new Set(kpisCompras.map(kpi => kpi.year))].sort((a, b) => b - a);
    return years;
  }, [kpisCompras]);

  // Función para manejar ordenamiento
  const handleSort = useCallback((field: keyof KpiComprasData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
    setCurrentPage(1);
  }, [sortField, sortDirection]);

  // Función para manejar dropdown de proveedores
  const toggleDropdown = useCallback((kpiId: string) => {
    setOpenDropdowns(prev => {
      const newSet = new Set(prev);
      if (newSet.has(kpiId)) {
        newSet.delete(kpiId);
      } else {
        newSet.add(kpiId);
      }
      return newSet;
    });
  }, []);

  // Función para manejar la confirmación de eliminación
  const handleDeleteConfirmation = useCallback((kpi: KpiComprasData) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPI',
      message: `¿Estás seguro de que quieres eliminar el KPI de compras de la semana ${kpi.weekNumber}/${kpi.year}? Esta acción no se puede deshacer.`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          await onDeleteKpi(kpi.id!);
          setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
        } catch (error) {
          console.error('Error al eliminar KPI de compras:', error);
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  }, [onDeleteKpi]);

  // Manejar selección de KPIs
  const handleSelectKpi = useCallback((kpiId: string) => {
    const newSelection = selectedKpis.includes(kpiId)
      ? selectedKpis.filter(id => id !== kpiId)
      : [...selectedKpis, kpiId];
    
    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [selectedKpis, onSelectionChange]);

  const handleSelectAll = useCallback(() => {
    const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
    const allSelected = currentPageIds.every(id => selectedKpis.includes(id));

    let newSelection;
    if (allSelected) {
      // Deseleccionar todos los de la página actual
      newSelection = selectedKpis.filter(id => !currentPageIds.includes(id));
    } else {
      // Seleccionar todos los de la página actual
      newSelection = [...new Set([...selectedKpis, ...currentPageIds])];
    }

    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [paginatedKpis, selectedKpis, onSelectionChange]);

  // Componente de fila de tabla memoizado para mejor rendimiento
  const TableRow = memo(({
    kpi,
    isSelected,
    onSelect,
    onEdit,
    onDelete,
    user: currentUser
  }: {
    kpi: KpiComprasData;
    isSelected: boolean;
    onSelect: (id: string) => void;
    onEdit: (kpi: KpiComprasData) => void;
    onDelete: (id: string) => void;
    user: User;
  }) => {
    // Preparar datos de proveedores - encontrar el proveedor con mayor porcentaje
    const proveedorPrincipal = kpi.distribucionProveedores.reduce((max, proveedor) =>
      proveedor.porcentaje > max.porcentaje ? proveedor : max
    );
    const otrosProveedores = kpi.distribucionProveedores.filter(p => p.nombre !== proveedorPrincipal.nombre);
    const tieneOtrosProveedores = otrosProveedores.length > 0;
    const isDropdownOpen = openDropdowns.has(kpi.id!);

    return (
      <tr className="bg-white hover:shadow transition-all duration-200 rounded-lg border border-gray-200">
        <td className="px-4 py-3 rounded-l-xl">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(kpi.id!)}
            className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
          />
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="flex items-center space-x-3">
            <div>
              <div className="text-sm font-medium text-gray-900">
                Semana {kpi.weekNumber}/{kpi.year}
              </div>
              <div className="text-xs text-gray-500">
                {formatDate(kpi.weekStartDate)} - {formatDate(kpi.weekEndDate)}
              </div>
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm font-medium text-gray-900">
            {kpi.numeroProveedoresActivos}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap text-center">
          <span className="text-sm font-medium text-gray-900">
            {kpi.porcentajeReporteGanancia.toFixed(1)}%
          </span>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm font-medium text-gray-900">
            ${kpi.preciosPromedioCompra.toFixed(2)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <span
            className={`text-sm font-medium ${kpi.diferencialPrecioPemex >= 0 ? '' : 'text-red-600'}`}
            style={kpi.diferencialPrecioPemex >= 0 ? { color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' } : {}}
          >
            {kpi.diferencialPrecioPemex >= 0 ? '+' : ''}${kpi.diferencialPrecioPemex.toFixed(2)}
          </span>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="w-full">
            <div className="flex justify-between items-center">
              <div className="flex w-full h-2 bg-gray-200 rounded-full overflow-hidden mr-3">
                {kpi.distribucionProveedores.map((proveedor, index) => {
                  const colors = ["#dc2626", "#16a34a", "#2563eb", "#ca8a04", "#9333ea"];
                  return (
                    <div
                      key={index}
                      className="flex flex-col justify-center overflow-hidden rounded-full transition-all duration-300"
                      role="progressbar"
                      aria-valuenow={proveedor.porcentaje}
                      aria-valuemin={0}
                      aria-valuemax={100}
                      style={{
                        width: `${proveedor.porcentaje}%`,
                        backgroundColor: colors[index % colors.length]
                      }}
                    ></div>
                  );
                })}
              </div>
              <div className="flex items-center space-x-2 relative">
                <span className="text-sm font-medium whitespace-nowrap text-gray-900">
                  {proveedorPrincipal.nombre}
                </span>
                {tieneOtrosProveedores && (
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(kpi.id!);
                      }}
                      className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                    >
                      <span>Ver todo</span>
                      <ChevronDown className={`h-3 w-3 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {isDropdownOpen && (
                      <div
                        className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-48"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="py-1">
                          {kpi.distribucionProveedores.map((proveedor: any, index: number) => {
                            const colors = ["#dc2626", "#16a34a", "#2563eb", "#ca8a04", "#9333ea"];
                            return (
                              <div key={index} className="px-3 py-2 flex items-center justify-between hover:bg-gray-50">
                                <div className="flex items-center space-x-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: colors[index % colors.length] }}
                                  ></div>
                                  <span className="text-sm text-gray-900">{proveedor.nombre}</span>
                                </div>
                                <span className="text-sm font-medium text-gray-600">
                                  {proveedor.porcentaje.toFixed(1)}%
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            {(kpi as any).user?.name || (kpi as any).user?.email || 'Usuario desconocido'}
          </div>
          <div className="text-xs text-gray-500">
            {(kpi as any).createdAt && new Date((kpi as any).createdAt).toLocaleDateString('es-ES')}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap text-right rounded-r-lg">
          <div className="flex items-center justify-end space-x-1">
            {/* Mostrar iconos para ADMIN y SUPER_ADMIN con funcionalidad */}
            {(currentUser.role === "ADMIN" || currentUser.role === "SUPER_ADMIN") && (
              <>
                <button
                  onClick={() => onEdit(kpi)}
                  className="p-1.5 text-gray-400 hover:text-primary hover:bg-primary/10 rounded transition-colors"
                  title="Editar KPI"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteConfirmation(kpi)}
                  className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                  title="Eliminar KPI"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </>
            )}
            {/* Para WORKER solo mostrar si es el creador */}
            {currentUser.role === "WORKER" && (kpi as any).user?.id === currentUser.id && (
              <button
                onClick={() => onEdit(kpi)}
                className="p-1.5 text-gray-400 hover:text-primary hover:bg-primary/10 rounded transition-colors"
                title="Editar KPI"
              >
                <Edit className="h-4 w-4" />
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  });

  return (
    <div className="bg-white shadow-sm border border-gray-200 border-t-0 rounded-b-xl overflow-hidden">

      {/* Filters */}
      {showFilters && (
        <div className="px-6 pt-3 bg-gray-50">
          <div className="flex ">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl w-full">
              {/* Campo de búsqueda - ocupa 2 columnas */}
              <div className="flex flex-col h-full md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buscar
                </label>
                <div className="relative w-full flex-grow">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar por semana.."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent h-10"
                  />
                </div>
              </div>

              {/* Selector de año - ocupa 1 columna */}
              <div className="flex flex-col h-full">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Año
                </label>
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => setSelectedYear(value === "all" ? "all" : parseInt(value))}
                >
                  <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                    <SelectValue placeholder="Seleccione un año" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los años</SelectItem>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Botón de limpiar - ocupa 1 columna */}
              <div className="flex flex-col h-full justify-end">
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedYear("all");
                    setCurrentPage(1);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors h-10"
                >
                  Limpiar filtros
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {kpisCompras.length === 0 ? (
        <div className="text-center bg-gray-50 py-12">
          <div className="text-gray-400 mb-3">
            <BarChart3 className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay KPIs registrados</h3>
          <p className="text-gray-500 mb-2">Comienza agregando datos semanales para ver el historial</p>
        </div>
      ) : filteredAndSortedKpis.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
          <p className="text-gray-500 mb-4">Intenta ajustar los filtros de búsqueda</p>
          <button
            onClick={() => {
              setSearchTerm("");
              setSelectedYear("all");
              setCurrentPage(1);
            }}
            className="text-primary hover:text-primary/80 font-medium"
          >
            Limpiar filtros
          </button>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto bg-gray-50 p-4">
            <table className="min-w-full border-separate border-spacing-y-2">
              <thead>
                <tr>
                  <th className="px-4 py-1 text-left bg-transparent">
                    <input
                      type="checkbox"
                      checked={paginatedKpis.length > 0 && paginatedKpis.every(kpi => selectedKpis.includes(kpi.id!))}
                      ref={(input) => {
                        if (input) {
                          const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
                          const selectedCurrentPage = currentPageIds.filter(id => selectedKpis.includes(id));
                          input.indeterminate = selectedCurrentPage.length > 0 && selectedCurrentPage.length < currentPageIds.length;
                        }
                      }}
                      onChange={handleSelectAll}
                      className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                    />
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('weekNumber')}
                  >
                    <Tooltip content="Semana del Año">
                      <div className="flex items-center space-x-1">
                        <span>Semana</span>
                        {sortField === 'weekNumber' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('numeroProveedoresActivos')}
                  >
                    <Tooltip content="Número de Proveedores Activos">
                      <div className="flex items-center space-x-1">
                        <span>Proveedores</span>
                        {sortField === 'numeroProveedoresActivos' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('porcentajeReporteGanancia')}
                  >
                    <Tooltip content="Porcentaje de Reporte de Ganancia Operativa (%)">
                      <div className="flex items-center space-x-1">
                        <span>% Reporte</span>
                        {sortField === 'porcentajeReporteGanancia' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Precio Promedio de Compra por Litro">
                      <span>Precio Promedio</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX">
                      <span>Diferencial PEMEX</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Distribución de Compras por Proveedor (%)">
                      <span>Distribución Proveedores</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Usuario que registró los datos">
                      <span>Usuario</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-right text-sm font-normal text-gray-400 bg-transparent">
                    <span>Acciones</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedKpis.map((kpi) => (
                  <TableRow
                    key={kpi.id}
                    kpi={kpi}
                    isSelected={selectedKpis.includes(kpi.id!)}
                    onSelect={handleSelectKpi}
                    onEdit={onEditKpi}
                    onDelete={() => handleDeleteConfirmation(kpi)}
                    user={user}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, filteredAndSortedKpis.length)} de {filteredAndSortedKpis.length} resultados
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <span className="px-3 py-1 text-sm font-medium">
                  {currentPage} de {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Dialog de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
        loading={confirmDialog.loading}
      />
    </div>
  );
};

export default KpiComprasHistorySection;
